#!/bin/bash

echo "========================================"
echo "   AI小说生成器 - 荒岛求生记"
echo "========================================"
echo

echo "正在检查环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "Node.js 已安装: $(node --version)"

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm，请先安装npm"
    exit 1
fi

echo "npm 已安装: $(npm --version)"

echo
echo "正在启动后端服务..."

# 检查后端依赖
if [ ! -d "backend/node_modules" ]; then
    echo "正在安装后端依赖..."
    cd backend
    npm install
    cd ..
fi

# 检查环境变量文件
if [ ! -f "backend/.env" ]; then
    echo "正在创建环境变量文件..."
    cp "backend/.env.example" "backend/.env"
    echo
    echo "重要提示: 请编辑 backend/.env 文件，设置您的 DeepSeek API Key"
    echo "文件位置: backend/.env"
    echo "设置项: DEEPSEEK_API_KEY=your_api_key_here"
    echo
    echo "如果没有API Key，系统将使用备用内容生成功能"
    echo
fi

# 启动后端服务（后台运行）
cd backend
npm start &
BACKEND_PID=$!
cd ..

echo "后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动
echo "等待后端服务启动..."
sleep 3

echo
echo "正在启动前端服务..."

# 检查前端依赖
if [ ! -d "frontend/node_modules" ]; then
    echo "正在安装前端依赖..."
    cd frontend
    npm install
    cd ..
fi

# 启动前端服务（后台运行）
cd frontend
npm run serve &
FRONTEND_PID=$!
cd ..

echo "前端服务已启动 (PID: $FRONTEND_PID)"

echo
echo "========================================"
echo "服务启动完成！"
echo "========================================"
echo
echo "后端服务: http://localhost:3000"
echo "前端服务: http://localhost:8080"
echo
echo "请等待几秒钟让服务完全启动，然后访问:"
echo "http://localhost:8080"
echo
echo "按 Ctrl+C 停止所有服务"

# 创建停止函数
cleanup() {
    echo
    echo "正在停止服务..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待用户中断
wait
