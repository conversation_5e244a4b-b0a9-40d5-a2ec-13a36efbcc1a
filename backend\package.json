{"name": "xiaoshuo-generator-backend", "version": "1.0.0", "description": "AI小说生成器后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test/run-all-tests.js", "test:ai": "node test/ai-service-test.js", "test:api": "node test/api-test.js"}, "keywords": ["ai", "novel", "generator", "game"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "body-parser": "^1.20.2", "fs-extra": "^11.1.1", "moment": "^2.29.4", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}}