帮我写一个AI小说生成器游戏，该生成器能够接受外部的信息，例如玩家的投喂信息，小说的背景是主角流落荒岛，然后每天会收到意外的包裹，包裹就是用户的投递。小说能够根据用户的投递产生新的剧情。

小说以第三人称上帝视角进行，以每天为单位，玩家投递收集器，每天只能进行一次投递，投递成功后，开始第二天。玩家每投递一次物资，开始新的小说书写。


前端架构采用vue2+axios开发

这是AI接口
curl https://api.deepseek.com/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <DeepSeek API Key>" \
  -d '{
        "model": "deepseek-chat",
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "Hello!"}
        ],
        "stream": false
      }'