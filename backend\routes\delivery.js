const express = require('express');
const router = express.Router();
const moment = require('moment');
const dataService = require('../services/dataService');
const aiService = require('../services/aiService');

// 投递物品
router.post('/', async (req, res) => {
  try {
    const { item, description } = req.body;

    // 验证输入
    if (!item || !item.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Item name is required'
      });
    }

    if (!description || !description.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Item description is required'
      });
    }

    // 获取当前游戏状态
    const gameState = await dataService.getGameState();

    // 新的游戏机制：每次投递后立即进入下一天，不再有时间限制
    // 玩家可以随时投递，每次投递都会推进游戏进度

    // 获取当前故事数据
    const storyData = await dataService.getStoryData();
    
    // 生成新的故事章节
    const aiResult = await aiService.generateStoryChapter(
      storyData,
      item.trim(),
      description.trim(),
      gameState.currentDay
    );

    // 创建新章节
    const newChapter = {
      day: gameState.currentDay,
      title: `第${gameState.currentDay}天：${item}`,
      content: aiResult.content,
      deliveredItem: {
        name: item.trim(),
        description: description.trim(),
        deliveryDate: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
      aiGenerated: aiResult.success,
      fallback: aiResult.fallback || false
    };

    // 保存新章节
    const chapterSaved = await dataService.addChapter(newChapter);
    if (!chapterSaved) {
      throw new Error('Failed to save new chapter');
    }

    // 更新游戏状态 - 投递后立即进入下一天
    gameState.currentDay += 1;
    gameState.lastDeliveryDate = new Date().toISOString();
    gameState.canDeliverToday = true; // 立即可以再次投递
    gameState.totalDeliveries += 1;
    gameState.playerStats.itemsDelivered.push({
      name: item.trim(),
      description: description.trim(),
      day: gameState.currentDay - 1,
      deliveryDate: new Date().toISOString()
    });
    gameState.playerStats.storyProgress = gameState.currentDay;

    const gameStateSaved = await dataService.saveGameState(gameState);
    if (!gameStateSaved) {
      throw new Error('Failed to save game state');
    }

    res.json({
      success: true,
      message: 'Item delivered successfully! Story generated and advanced to next day.',
      data: {
        chapter: newChapter,
        gameState: {
          currentDay: gameState.currentDay,
          canDeliverToday: true, // 立即可以再次投递
          totalDeliveries: gameState.totalDeliveries
        },
        aiInfo: {
          generated: aiResult.success,
          fallback: aiResult.fallback || false,
          usage: aiResult.usage
        }
      }
    });

  } catch (error) {
    console.error('Error processing delivery:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process delivery',
      details: error.message
    });
  }
});

// 获取投递历史
router.get('/history', async (req, res) => {
  try {
    const gameState = await dataService.getGameState();
    
    res.json({
      success: true,
      data: {
        totalDeliveries: gameState.totalDeliveries,
        items: gameState.playerStats.itemsDelivered
      }
    });
  } catch (error) {
    console.error('Error fetching delivery history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch delivery history'
    });
  }
});

// 检查投递状态
router.get('/status', async (req, res) => {
  try {
    const gameState = await dataService.getGameState();
    const canDeliver = await checkCanDeliverToday(gameState);

    res.json({
      success: true,
      data: {
        canDeliverToday: canDeliver,
        lastDeliveryDate: gameState.lastDeliveryDate,
        totalDeliveries: gameState.totalDeliveries,
        nextDeliveryAvailable: canDeliver ? 'now' : 'tomorrow'
      }
    });
  } catch (error) {
    console.error('Error checking delivery status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check delivery status'
    });
  }
});

// 检查今天是否可以投递
async function checkCanDeliverToday(gameState) {
  if (!gameState.lastDeliveryDate) {
    return true;
  }

  const lastDelivery = moment(gameState.lastDeliveryDate);
  const today = moment();
  
  // 如果上次投递不是今天，则可以投递
  return !lastDelivery.isSame(today, 'day');
}

module.exports = router;
