<template>
  <div class="story">
    <!-- 故事标题 -->
    <el-card class="title-card" shadow="hover">
      <div class="story-header">
        <h1 class="story-title">{{ storyData.title || '荒岛求生记' }}</h1>
        <div class="story-meta">
          <el-tag type="info">共 {{ storyData.chapters ? storyData.chapters.length : 0 }} 章</el-tag>
          <el-tag type="success" style="margin-left: 10px;">
            第 {{ storyData.currentDay || 1 }} 天
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 章节列表 -->
    <div class="chapters-container" v-loading="loading">
      <el-timeline v-if="storyData.chapters && storyData.chapters.length > 0">
        <el-timeline-item
          v-for="(chapter, index) in storyData.chapters"
          :key="chapter.day"
          :timestamp="formatTime(chapter.timestamp)"
          :type="getTimelineType(chapter)"
          :icon="getTimelineIcon(chapter)"
          size="large"
        >
          <el-card class="chapter-card" shadow="hover">
            <div slot="header" class="chapter-header">
              <span class="chapter-title">{{ chapter.title }}</span>
              <div class="chapter-actions">
                <el-tag v-if="chapter.deliveredItem" size="mini" type="primary">
                  {{ chapter.deliveredItem.name }}
                </el-tag>
                <el-button 
                  type="text" 
                  size="mini" 
                  @click="toggleChapterExpand(index)"
                  :icon="expandedChapters.includes(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                >
                  {{ expandedChapters.includes(index) ? '收起' : '展开' }}
                </el-button>
              </div>
            </div>
            
            <div class="chapter-content">
              <div 
                v-if="expandedChapters.includes(index) || chapter.content.length <= 200"
                class="full-content"
              >
                {{ chapter.content }}
              </div>
              <div v-else class="preview-content">
                {{ chapter.content.substring(0, 200) }}...
              </div>
              
              <!-- 投递物品信息 -->
              <div v-if="chapter.deliveredItem" class="delivered-item">
                <el-divider content-position="left">
                  <i class="el-icon-box"></i>
                  投递物品
                </el-divider>
                <div class="item-info">
                  <p><strong>物品名称：</strong>{{ chapter.deliveredItem.name }}</p>
                  <p><strong>物品描述：</strong>{{ chapter.deliveredItem.description }}</p>
                  <p><strong>投递时间：</strong>{{ formatTime(chapter.deliveredItem.deliveryDate) }}</p>
                </div>
              </div>
              
              <!-- AI生成标识 -->
              <div class="chapter-footer">
                <div class="generation-info">
                  <el-tag 
                    v-if="chapter.aiGenerated !== undefined" 
                    :type="chapter.aiGenerated ? 'success' : 'warning'"
                    size="mini"
                  >
                    {{ chapter.aiGenerated ? 'AI生成' : '备用内容' }}
                  </el-tag>
                </div>
                <div class="chapter-day">
                  第 {{ chapter.day }} 天
                </div>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      
      <!-- 空状态 -->
      <el-empty v-else description="还没有故事章节" />
    </div>

    <!-- 操作按钮 -->
    <div class="story-actions">
      <el-button type="primary" @click="refreshStory" :loading="loading">
        <i class="el-icon-refresh"></i>
        刷新故事
      </el-button>
      <el-button type="success" @click="$router.push('/delivery')">
        <i class="el-icon-box"></i>
        投递物品
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Story',
  data() {
    return {
      storyData: {},
      loading: false,
      expandedChapters: []
    }
  },
  async created() {
    await this.loadStory()
  },
  methods: {
    async loadStory() {
      this.loading = true
      try {
        const response = await this.$http.get('/story')
        if (response.data.success) {
          this.storyData = response.data.data
          // 默认展开最新的章节
          if (this.storyData.chapters && this.storyData.chapters.length > 0) {
            this.expandedChapters = [this.storyData.chapters.length - 1]
          }
        }
      } catch (error) {
        console.error('Failed to load story:', error)
        this.$message.error('加载故事失败')
      } finally {
        this.loading = false
      }
    },
    
    async refreshStory() {
      await this.loadStory()
      this.$message.success('故事已刷新')
    },
    
    toggleChapterExpand(index) {
      const expandedIndex = this.expandedChapters.indexOf(index)
      if (expandedIndex > -1) {
        this.expandedChapters.splice(expandedIndex, 1)
      } else {
        this.expandedChapters.push(index)
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString('zh-CN')
    },
    
    getTimelineType(chapter) {
      if (chapter.day === 0) return 'info'
      if (chapter.deliveredItem) return 'primary'
      return 'success'
    },
    
    getTimelineIcon(chapter) {
      if (chapter.day === 0) return 'el-icon-star-on'
      if (chapter.deliveredItem) return 'el-icon-box'
      return 'el-icon-edit'
    }
  }
}
</script>

<style scoped>
.story {
  max-width: 800px;
  margin: 0 auto;
}

.title-card {
  margin-bottom: 20px;
}

.story-header {
  text-align: center;
}

.story-title {
  color: #303133;
  margin-bottom: 15px;
  font-size: 28px;
}

.story-meta {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.chapters-container {
  margin-bottom: 20px;
}

.chapter-card {
  margin-bottom: 0;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-title {
  font-weight: bold;
  color: #303133;
}

.chapter-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chapter-content {
  line-height: 1.8;
  color: #606266;
}

.full-content,
.preview-content {
  white-space: pre-wrap;
  margin-bottom: 15px;
}

.delivered-item {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.item-info p {
  margin: 5px 0;
  color: #606266;
}

.chapter-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.chapter-day {
  font-size: 12px;
  color: #909399;
}

.story-actions {
  text-align: center;
  padding: 20px 0;
}

.story-actions .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .story {
    padding: 0 10px;
  }
  
  .story-title {
    font-size: 24px;
  }
  
  .chapter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chapter-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .story-actions .el-button {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}
</style>
