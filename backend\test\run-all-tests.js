const { spawn } = require('child_process');
const path = require('path');

class TestRunner {
  constructor() {
    this.testResults = {};
    this.totalTests = 0;
    this.passedTests = 0;
  }

  async runTest(testName, testFile) {
    console.log(`\n🧪 运行测试: ${testName}`);
    console.log('='.repeat(50));

    return new Promise((resolve) => {
      const testProcess = spawn('node', [testFile], {
        cwd: path.dirname(testFile),
        stdio: 'inherit'
      });

      testProcess.on('close', (code) => {
        const passed = code === 0;
        this.testResults[testName] = passed;
        this.totalTests++;
        if (passed) {
          this.passedTests++;
          console.log(`✅ ${testName}: PASSED`);
        } else {
          console.log(`❌ ${testName}: FAILED`);
        }
        resolve(passed);
      });

      testProcess.on('error', (error) => {
        console.error(`测试运行错误: ${error.message}`);
        this.testResults[testName] = false;
        this.totalTests++;
        resolve(false);
      });
    });
  }

  async runAllTests() {
    console.log('🚀 开始运行所有测试...\n');

    const tests = [
      {
        name: 'AI服务测试',
        file: path.join(__dirname, 'ai-service-test.js')
      },
      {
        name: 'API接口测试',
        file: path.join(__dirname, 'api-test.js')
      }
    ];

    // 运行所有测试
    for (const test of tests) {
      await this.runTest(test.name, test.file);
      
      // 测试之间的间隔
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 输出测试结果汇总
    this.printSummary();

    return this.passedTests === this.totalTests;
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果汇总');
    console.log('='.repeat(60));

    Object.entries(this.testResults).forEach(([testName, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      console.log(`${status} - ${testName}`);
    });

    console.log('\n' + '-'.repeat(60));
    console.log(`总计: ${this.passedTests}/${this.totalTests} 测试通过`);
    
    if (this.passedTests === this.totalTests) {
      console.log('🎉 所有测试通过！系统运行正常');
    } else {
      console.log('⚠️  部分测试失败，请检查系统状态');
    }
    console.log('='.repeat(60));
  }

  async checkPrerequisites() {
    console.log('🔍 检查测试前置条件...\n');

    // 检查Node.js版本
    const nodeVersion = process.version;
    console.log(`Node.js版本: ${nodeVersion}`);

    // 检查必要的依赖
    try {
      require('axios');
      console.log('✅ axios 依赖正常');
    } catch (error) {
      console.log('❌ axios 依赖缺失');
      return false;
    }

    try {
      require('moment');
      console.log('✅ moment 依赖正常');
    } catch (error) {
      console.log('❌ moment 依赖缺失');
      return false;
    }

    // 检查数据目录
    const fs = require('fs');
    const dataDir = path.join(__dirname, '..', 'data');
    if (fs.existsSync(dataDir)) {
      console.log('✅ 数据目录存在');
    } else {
      console.log('⚠️  数据目录不存在，将自动创建');
    }

    console.log('✅ 前置条件检查完成\n');
    return true;
  }

  async startTestServer() {
    console.log('🚀 启动测试服务器...');
    
    return new Promise((resolve) => {
      const serverProcess = spawn('node', ['server.js'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'pipe'
      });

      let serverStarted = false;

      serverProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Server is running') && !serverStarted) {
          serverStarted = true;
          console.log('✅ 测试服务器启动成功');
          resolve(serverProcess);
        }
      });

      serverProcess.stderr.on('data', (data) => {
        console.error(`服务器错误: ${data}`);
      });

      // 超时处理
      setTimeout(() => {
        if (!serverStarted) {
          console.log('⚠️  服务器启动超时，继续测试...');
          resolve(serverProcess);
        }
      }, 10000);
    });
  }

  async stopTestServer(serverProcess) {
    if (serverProcess) {
      console.log('🛑 停止测试服务器...');
      serverProcess.kill();
      
      // 等待进程结束
      await new Promise(resolve => {
        serverProcess.on('close', resolve);
        setTimeout(resolve, 2000); // 超时保护
      });
      
      console.log('✅ 测试服务器已停止');
    }
  }
}

async function main() {
  const runner = new TestRunner();

  try {
    // 检查前置条件
    const prerequisitesOk = await runner.checkPrerequisites();
    if (!prerequisitesOk) {
      console.log('❌ 前置条件检查失败，退出测试');
      process.exit(1);
    }

    // 启动测试服务器
    const serverProcess = await runner.startTestServer();

    // 等待服务器完全启动
    console.log('⏳ 等待服务器完全启动...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    try {
      // 运行所有测试
      const allTestsPassed = await runner.runAllTests();

      // 根据测试结果设置退出码
      process.exit(allTestsPassed ? 0 : 1);
    } finally {
      // 确保服务器被停止
      await runner.stopTestServer(serverProcess);
    }

  } catch (error) {
    console.error('测试运行器发生错误:', error);
    process.exit(1);
  }
}

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n测试被用户中断');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n测试被终止');
  process.exit(1);
});

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main();
}

module.exports = TestRunner;
