<template>
  <div class="home">
    <!-- 欢迎卡片 -->
    <el-card class="welcome-card" shadow="hover">
      <div class="welcome-content">
        <div class="welcome-text">
          <h2>欢迎来到荒岛求生记</h2>
          <p>在这个神奇的世界里，你可以通过投递物品来影响主角李明的荒岛生活。每一个物品都会带来新的故事章节，让我们一起见证这个奇妙的冒险吧！</p>
        </div>
        <div class="welcome-image">
          <i class="el-icon-sunny" style="font-size: 80px; color: #f39c12;"></i>
        </div>
      </div>
    </el-card>

    <!-- 游戏状态 -->
    <el-row :gutter="20" class="status-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="status-card">
          <div class="status-item">
            <i class="el-icon-calendar status-icon"></i>
            <div class="status-info">
              <div class="status-value">第 {{ gameStatus.currentDay || 1 }} 天</div>
              <div class="status-label">当前天数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="status-card">
          <div class="status-item">
            <i class="el-icon-box status-icon"></i>
            <div class="status-info">
              <div class="status-value">{{ gameStatus.totalDeliveries || 0 }}</div>
              <div class="status-label">总投递次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="status-card">
          <div class="status-item">
            <i class="el-icon-check status-icon" :class="{ 'can-deliver': gameStatus.canDeliverToday }"></i>
            <div class="status-info">
              <div class="status-value">{{ gameStatus.canDeliverToday ? '可投递' : '已投递' }}</div>
              <div class="status-label">今日状态</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="status-card">
          <div class="status-item">
            <i class="el-icon-notebook-1 status-icon"></i>
            <div class="status-info">
              <div class="status-value">{{ storyStats.totalChapters || 0 }}</div>
              <div class="status-label">故事章节</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新章节预览 -->
    <el-card class="latest-chapter-card" shadow="hover" v-if="latestChapter">
      <div slot="header" class="card-header">
        <span>最新章节</span>
        <el-button type="text" @click="$router.push('/story')">查看全部</el-button>
      </div>
      <div class="chapter-preview">
        <h3 class="chapter-title">{{ latestChapter.title }}</h3>
        <p class="chapter-content">{{ latestChapter.content.substring(0, 200) }}...</p>
        <div class="chapter-meta">
          <el-tag size="mini" v-if="latestChapter.deliveredItem">
            物品：{{ latestChapter.deliveredItem.name }}
          </el-tag>
          <span class="chapter-time">{{ formatTime(latestChapter.timestamp) }}</span>
        </div>
      </div>
    </el-card>

    <!-- 快速操作 -->
    <el-card class="quick-actions-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>快速操作</span>
      </div>
      <div class="quick-actions">
        <el-button 
          type="primary" 
          size="large" 
          :disabled="!gameStatus.canDeliverToday"
          @click="$router.push('/delivery')"
          class="action-button"
        >
          <i class="el-icon-box"></i>
          {{ gameStatus.canDeliverToday ? '投递物品' : '今日已投递' }}
        </el-button>
        
        <el-button 
          type="success" 
          size="large" 
          @click="$router.push('/story')"
          class="action-button"
        >
          <i class="el-icon-reading"></i>
          阅读故事
        </el-button>
        
        <el-button 
          type="info" 
          size="large" 
          @click="$router.push('/stats')"
          class="action-button"
        >
          <i class="el-icon-data-analysis"></i>
          查看统计
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      gameStatus: {},
      storyStats: {},
      latestChapter: null,
      loading: false
    }
  },
  async created() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadGameStatus(),
          this.loadStoryStats(),
          this.loadLatestChapter()
        ])
      } catch (error) {
        console.error('Failed to load data:', error)
      } finally {
        this.loading = false
      }
    },
    
    async loadGameStatus() {
      try {
        const response = await this.$http.get('/game/status')
        if (response.data.success) {
          this.gameStatus = response.data.data
          this.$emit('game-status-updated', this.gameStatus)
        }
      } catch (error) {
        console.error('Failed to load game status:', error)
      }
    },
    
    async loadStoryStats() {
      try {
        const response = await this.$http.get('/story/stats')
        if (response.data.success) {
          this.storyStats = response.data.data
        }
      } catch (error) {
        console.error('Failed to load story stats:', error)
      }
    },
    
    async loadLatestChapter() {
      try {
        const response = await this.$http.get('/story/latest')
        if (response.data.success) {
          this.latestChapter = response.data.data
        }
      } catch (error) {
        console.error('Failed to load latest chapter:', error)
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcome-text {
  flex: 1;
}

.welcome-text h2 {
  color: #303133;
  margin-bottom: 10px;
}

.welcome-text p {
  color: #606266;
  line-height: 1.6;
}

.welcome-image {
  margin-left: 20px;
}

.status-row {
  margin-bottom: 20px;
}

.status-card {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 32px;
  color: #909399;
  margin-right: 15px;
}

.status-icon.can-deliver {
  color: #67c23a;
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.latest-chapter-card,
.quick-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-preview {
  padding: 10px 0;
}

.chapter-title {
  color: #303133;
  margin-bottom: 10px;
}

.chapter-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
}

.chapter-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-time {
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-button {
  flex: 1;
  min-width: 120px;
}

.action-button i {
  margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-image {
    margin-left: 0;
    margin-top: 20px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}
</style>
