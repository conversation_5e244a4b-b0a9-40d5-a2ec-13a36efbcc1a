module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },

  // DeepSeek API配置
  deepseek: {
    apiUrl: 'https://api.deepseek.com/chat/completions',
    apiKey: process.env.DEEPSEEK_API_KEY || '', // 需要在环境变量中设置
    model: 'deepseek-chat'
  },

  // 游戏配置
  game: {
    maxDeliveriesPerDay: 1, // 每天最大投递次数
    storyPromptTemplate: `你是一个专业的小说作家。请根据以下背景和新投递的物品，继续写小说的下一章节。

背景设定：主角流落在一个荒岛上，每天都会收到神秘的包裹。这些包裹里的物品会影响主角的生活和岛上的故事发展。

当前故事内容：
{currentStory}

今天收到的新物品：{newItem}
物品描述：{itemDescription}

请以第三人称上帝视角，写出今天（第{dayNumber}天）的故事章节。要求：
1. 字数控制在300-500字
2. 要体现新物品对故事的影响
3. 保持故事的连贯性和趣味性
4. 可以适当增加一些悬念或转折

请直接输出故事内容，不要包含其他说明文字。`
  },

  // 数据存储配置
  storage: {
    dataDir: './data',
    storyFile: 'story.json',
    gameStateFile: 'gameState.json'
  }
};
