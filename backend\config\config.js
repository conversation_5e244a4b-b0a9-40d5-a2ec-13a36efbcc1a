module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },

  // DeepSeek API配置
  deepseek: {
    apiUrl: 'https://api.deepseek.com/chat/completions',
    apiKey: process.env.DEEPSEEK_API_KEY || '', // 需要在环境变量中设置
    model: 'deepseek-chat'
  },

  // 游戏配置
  game: {
    maxDeliveriesPerDay: 1, // 每次投递后进入下一天
    storyPromptTemplate: `你是一个专业的小说作家。请根据以下背景和新投递的物品，写出完整详细的故事章节。

背景设定：主角李明流落在一个荒岛上，每天都会收到神秘的包裹。这些包裹里的物品会影响主角的生活和岛上的故事发展。

当前故事内容：
{currentStory}

今天收到的新物品：{newItem}
物品描述：{itemDescription}

请以第三人称上帝视角，写出今天（第{dayNumber}天）完整的故事章节。要求：
1. 字数控制在500-800字，内容要丰富详细
2. 详细描述李明发现包裹的过程
3. 描述李明如何使用这个物品，以及物品带来的具体帮助
4. 描述李明一整天的活动：早晨、上午、下午、傍晚的具体行为
5. 体现物品对李明生存状况的实际改善
6. 保持故事的连贯性，与之前的情节呼应
7. 可以适当增加环境描写、心理活动和一些悬念
8. 结尾要为下一天的故事留下期待

请直接输出故事内容，不要包含其他说明文字。`
  },

  // 数据存储配置
  storage: {
    dataDir: './data',
    storyFile: 'story.json',
    gameStateFile: 'gameState.json'
  }
};
