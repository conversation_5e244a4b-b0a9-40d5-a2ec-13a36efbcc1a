module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },

  // DeepSeek API配置
  deepseek: {
    apiUrl: 'https://api.deepseek.com/chat/completions',
    apiKey: process.env.DEEPSEEK_API_KEY || '', // 需要在环境变量中设置
    model: 'deepseek-chat'
  },

  // 游戏配置
  game: {
    maxDeliveriesPerDay: 1, // 每次投递后进入下一天
    storyPromptTemplate: `你是一个专业的荒岛生存小说作家。请根据以下背景和新获得的物品，续写荒岛生存故事。

背景设定：主角李明流落在一个荒岛上，正在努力生存。他偶尔会获得一些有用的物品（可能是海浪冲上来的，或者从废墟中找到的），这些物品会帮助他改善生存条件。

当前故事内容：
{currentStory}

李明新获得的物品：{newItem}
物品描述：{itemDescription}

请以第三人称上帝视角，续写第{dayNumber}天的荒岛生存故事。要求：
1. 字数控制在500-800字，内容要丰富详细
2. 合理描述李明如何获得这个物品（海浪冲上岸、在岛上发现、从残骸中找到等）
3. 重点描述李明如何巧妙地使用这个物品来解决生存问题
4. 展现真实的荒岛生存挑战：寻找食物、制作工具、搭建庇护所、生火、净水等
5. 体现物品对李明生存技能和生活质量的实际提升
6. 描述李明的生存智慧和创造力，如何将物品用于多种用途
7. 包含环境描写、动物互动、天气变化等荒岛元素
8. 保持故事的真实感和生存感，避免过于依赖外来帮助
9. 展现李明的心理变化：从绝望到希望，从恐惧到自信

请直接输出故事内容，不要包含其他说明文字。`
  },

  // 数据存储配置
  storage: {
    dataDir: './data',
    storyFile: 'story.json',
    gameStateFile: 'gameState.json'
  }
};
