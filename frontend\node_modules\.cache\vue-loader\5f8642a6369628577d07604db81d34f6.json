{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue", "mtime": 1756199881921}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AA8HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"home\">\n    <!-- 欢迎卡片 -->\n    <el-card class=\"welcome-card\" shadow=\"hover\">\n      <div class=\"welcome-content\">\n        <div class=\"welcome-text\">\n          <h2>欢迎来到荒岛求生记</h2>\n          <p>在这个神奇的世界里，你可以通过投递物品来影响主角李明的荒岛生活。每一个物品都会带来新的故事章节，让我们一起见证这个奇妙的冒险吧！</p>\n        </div>\n        <div class=\"welcome-image\">\n          <i class=\"el-icon-sunny\" style=\"font-size: 80px; color: #f39c12;\"></i>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 游戏状态 -->\n    <el-row :gutter=\"20\" class=\"status-row\">\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-calendar status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">第 {{ gameStatus.currentDay || 1 }} 天</div>\n              <div class=\"status-label\">当前天数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-box status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ gameStatus.totalDeliveries || 0 }}</div>\n              <div class=\"status-label\">总投递次数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-check status-icon\" :class=\"{ 'can-deliver': gameStatus.canDeliverToday }\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ gameStatus.canDeliverToday ? '可投递' : '已投递' }}</div>\n              <div class=\"status-label\">今日状态</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-notebook-1 status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ storyStats.totalChapters || 0 }}</div>\n              <div class=\"status-label\">故事章节</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 最新章节预览 -->\n    <el-card class=\"latest-chapter-card\" shadow=\"hover\" v-if=\"latestChapter\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>最新章节</span>\n        <el-button type=\"text\" @click=\"$router.push('/story')\">查看全部</el-button>\n      </div>\n      <div class=\"chapter-preview\">\n        <h3 class=\"chapter-title\">{{ latestChapter.title }}</h3>\n        <p class=\"chapter-content\">{{ latestChapter.content.substring(0, 200) }}...</p>\n        <div class=\"chapter-meta\">\n          <el-tag size=\"mini\" v-if=\"latestChapter.deliveredItem\">\n            物品：{{ latestChapter.deliveredItem.name }}\n          </el-tag>\n          <span class=\"chapter-time\">{{ formatTime(latestChapter.timestamp) }}</span>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 快速操作 -->\n    <el-card class=\"quick-actions-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>快速操作</span>\n      </div>\n      <div class=\"quick-actions\">\n        <el-button \n          type=\"primary\" \n          size=\"large\" \n          :disabled=\"!gameStatus.canDeliverToday\"\n          @click=\"$router.push('/delivery')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-box\"></i>\n          {{ gameStatus.canDeliverToday ? '投递物品' : '今日已投递' }}\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          size=\"large\" \n          @click=\"$router.push('/story')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-reading\"></i>\n          阅读故事\n        </el-button>\n        \n        <el-button \n          type=\"info\" \n          size=\"large\" \n          @click=\"$router.push('/stats')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-data-analysis\"></i>\n          查看统计\n        </el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      gameStatus: {},\n      storyStats: {},\n      latestChapter: null,\n      loading: false\n    }\n  },\n  async created() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      this.loading = true\n      try {\n        await Promise.all([\n          this.loadGameStatus(),\n          this.loadStoryStats(),\n          this.loadLatestChapter()\n        ])\n      } catch (error) {\n        console.error('Failed to load data:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadGameStatus() {\n      try {\n        const response = await this.$http.get('/game/status')\n        if (response.data.success) {\n          this.gameStatus = response.data.data\n          this.$emit('game-status-updated', this.gameStatus)\n        }\n      } catch (error) {\n        console.error('Failed to load game status:', error)\n      }\n    },\n    \n    async loadStoryStats() {\n      try {\n        const response = await this.$http.get('/story/stats')\n        if (response.data.success) {\n          this.storyStats = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load story stats:', error)\n      }\n    },\n    \n    async loadLatestChapter() {\n      try {\n        const response = await this.$http.get('/story/latest')\n        if (response.data.success) {\n          this.latestChapter = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load latest chapter:', error)\n      }\n    },\n    \n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.home {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.welcome-card {\n  margin-bottom: 20px;\n}\n\n.welcome-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.welcome-text {\n  flex: 1;\n}\n\n.welcome-text h2 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.welcome-text p {\n  color: #606266;\n  line-height: 1.6;\n}\n\n.welcome-image {\n  margin-left: 20px;\n}\n\n.status-row {\n  margin-bottom: 20px;\n}\n\n.status-card {\n  margin-bottom: 20px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n}\n\n.status-icon {\n  font-size: 32px;\n  color: #909399;\n  margin-right: 15px;\n}\n\n.status-icon.can-deliver {\n  color: #67c23a;\n}\n\n.status-info {\n  flex: 1;\n}\n\n.status-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.status-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.latest-chapter-card,\n.quick-actions-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-preview {\n  padding: 10px 0;\n}\n\n.chapter-title {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.chapter-content {\n  color: #606266;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.chapter-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n.quick-actions {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.action-button {\n  flex: 1;\n  min-width: 120px;\n}\n\n.action-button i {\n  margin-right: 5px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .welcome-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .welcome-image {\n    margin-left: 0;\n    margin-top: 20px;\n  }\n  \n  .quick-actions {\n    flex-direction: column;\n  }\n  \n  .action-button {\n    width: 100%;\n  }\n}\n</style>\n"]}]}