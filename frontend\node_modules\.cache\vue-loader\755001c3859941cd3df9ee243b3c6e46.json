{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue?vue&type=template&id=5c98942e&scoped=true", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue", "mtime": 1756199973404}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}