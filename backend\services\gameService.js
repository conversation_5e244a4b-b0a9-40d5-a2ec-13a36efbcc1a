const moment = require('moment');
const dataService = require('./dataService');

class GameService {
  constructor() {
    this.maxDeliveriesPerDay = 1;
  }

  // 检查今天是否可以投递
  async canDeliverToday() {
    try {
      const gameState = await dataService.getGameState();
      
      if (!gameState.lastDeliveryDate) {
        return true;
      }

      const lastDelivery = moment(gameState.lastDeliveryDate);
      const today = moment();
      
      // 如果上次投递不是今天，则可以投递
      return !lastDelivery.isSame(today, 'day');
    } catch (error) {
      console.error('Error checking delivery status:', error);
      return false;
    }
  }

  // 获取下次可投递时间
  async getNextDeliveryTime() {
    try {
      const gameState = await dataService.getGameState();
      
      if (!gameState.lastDeliveryDate) {
        return new Date(); // 立即可投递
      }

      const lastDelivery = moment(gameState.lastDeliveryDate);
      const nextDelivery = lastDelivery.clone().add(1, 'day').startOf('day');
      
      return nextDelivery.toDate();
    } catch (error) {
      console.error('Error calculating next delivery time:', error);
      return new Date();
    }
  }

  // 计算游戏持续天数
  async getGameDuration() {
    try {
      const gameState = await dataService.getGameState();
      
      if (!gameState.gameStartDate) {
        return 0;
      }

      const startDate = moment(gameState.gameStartDate);
      const today = moment();
      
      return today.diff(startDate, 'days');
    } catch (error) {
      console.error('Error calculating game duration:', error);
      return 0;
    }
  }

  // 更新游戏状态（投递后）
  async updateGameStateAfterDelivery(item, description) {
    try {
      const gameState = await dataService.getGameState();
      
      // 更新基本状态
      gameState.currentDay += 1;
      gameState.lastDeliveryDate = new Date().toISOString();
      gameState.totalDeliveries += 1;
      
      // 更新玩家统计
      gameState.playerStats.itemsDelivered.push({
        name: item,
        description: description,
        day: gameState.currentDay - 1,
        deliveryDate: new Date().toISOString()
      });
      gameState.playerStats.storyProgress = gameState.currentDay;
      
      // 保存状态
      await dataService.saveGameState(gameState);
      
      return gameState;
    } catch (error) {
      console.error('Error updating game state:', error);
      throw error;
    }
  }

  // 获取玩家成就
  async getPlayerAchievements() {
    try {
      const gameState = await dataService.getGameState();
      const storyData = await dataService.getStoryData();
      
      const achievements = [];
      
      // 成就1：初来乍到
      if (gameState.totalDeliveries > 0) {
        achievements.push({
          id: 'first_delivery',
          name: '初来乍到',
          description: '投递第一个物品',
          unlocked: true,
          unlockedDate: gameState.playerStats.itemsDelivered[0]?.deliveryDate
        });
      }
      
      // 成就2：生存专家
      if (gameState.currentDay > 7) {
        achievements.push({
          id: 'survival_expert',
          name: '生存专家',
          description: '生存超过7天',
          unlocked: true,
          unlockedDate: this.calculateAchievementDate(gameState, 7)
        });
      }
      
      // 成就3：物资丰富
      if (gameState.totalDeliveries >= 10) {
        achievements.push({
          id: 'rich_supplies',
          name: '物资丰富',
          description: '投递超过10个物品',
          unlocked: true,
          unlockedDate: gameState.playerStats.itemsDelivered[9]?.deliveryDate
        });
      }
      
      // 成就4：故事大师
      const totalWords = storyData.chapters.reduce((total, chapter) => {
        return total + (chapter.content ? chapter.content.length : 0);
      }, 0);
      
      if (totalWords > 5000) {
        achievements.push({
          id: 'story_master',
          name: '故事大师',
          description: '故事总字数超过5000字',
          unlocked: true,
          unlockedDate: this.findStoryMasterDate(storyData.chapters)
        });
      }
      
      // 成就5：连续投递
      const consecutiveDays = this.calculateConsecutiveDays(gameState.playerStats.itemsDelivered);
      if (consecutiveDays >= 5) {
        achievements.push({
          id: 'consecutive_delivery',
          name: '坚持不懈',
          description: '连续5天投递物品',
          unlocked: true,
          unlockedDate: this.findConsecutiveAchievementDate(gameState.playerStats.itemsDelivered, 5)
        });
      }
      
      return achievements;
    } catch (error) {
      console.error('Error getting player achievements:', error);
      return [];
    }
  }

  // 计算连续投递天数
  calculateConsecutiveDays(deliveredItems) {
    if (!deliveredItems || deliveredItems.length === 0) {
      return 0;
    }

    // 按日期排序
    const sortedItems = deliveredItems.sort((a, b) => 
      new Date(a.deliveryDate) - new Date(b.deliveryDate)
    );

    let maxConsecutive = 1;
    let currentConsecutive = 1;

    for (let i = 1; i < sortedItems.length; i++) {
      const prevDate = moment(sortedItems[i - 1].deliveryDate);
      const currentDate = moment(sortedItems[i].deliveryDate);
      
      const daysDiff = currentDate.diff(prevDate, 'days');
      
      if (daysDiff === 1) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 1;
      }
    }

    return maxConsecutive;
  }

  // 计算成就解锁日期
  calculateAchievementDate(gameState, targetDay) {
    if (!gameState.gameStartDate) {
      return new Date().toISOString();
    }
    
    const startDate = moment(gameState.gameStartDate);
    const achievementDate = startDate.clone().add(targetDay - 1, 'days');
    
    return achievementDate.toISOString();
  }

  // 找到故事大师成就的解锁日期
  findStoryMasterDate(chapters) {
    let totalWords = 0;
    
    for (const chapter of chapters) {
      totalWords += chapter.content ? chapter.content.length : 0;
      if (totalWords > 5000) {
        return chapter.timestamp;
      }
    }
    
    return new Date().toISOString();
  }

  // 找到连续投递成就的解锁日期
  findConsecutiveAchievementDate(deliveredItems, targetDays) {
    if (!deliveredItems || deliveredItems.length < targetDays) {
      return new Date().toISOString();
    }

    const sortedItems = deliveredItems.sort((a, b) => 
      new Date(a.deliveryDate) - new Date(b.deliveryDate)
    );

    let consecutiveCount = 1;

    for (let i = 1; i < sortedItems.length; i++) {
      const prevDate = moment(sortedItems[i - 1].deliveryDate);
      const currentDate = moment(sortedItems[i].deliveryDate);
      
      const daysDiff = currentDate.diff(prevDate, 'days');
      
      if (daysDiff === 1) {
        consecutiveCount++;
        if (consecutiveCount >= targetDays) {
          return sortedItems[i].deliveryDate;
        }
      } else {
        consecutiveCount = 1;
      }
    }

    return new Date().toISOString();
  }

  // 重置游戏
  async resetGame() {
    try {
      // 重置游戏状态
      const newGameState = {
        currentDay: 1,
        lastDeliveryDate: null,
        canDeliverToday: true,
        totalDeliveries: 0,
        gameStartDate: new Date().toISOString(),
        playerStats: {
          itemsDelivered: [],
          storyProgress: 1
        }
      };

      // 重置故事数据
      const newStoryData = dataService.getDefaultStoryData();

      await dataService.saveGameState(newGameState);
      await dataService.saveStoryData(newStoryData);

      return { gameState: newGameState, storyData: newStoryData };
    } catch (error) {
      console.error('Error resetting game:', error);
      throw error;
    }
  }

  // 获取游戏统计信息
  async getGameStatistics() {
    try {
      const gameState = await dataService.getGameState();
      const storyData = await dataService.getStoryData();
      const achievements = await this.getPlayerAchievements();

      const totalWords = storyData.chapters.reduce((total, chapter) => {
        return total + (chapter.content ? chapter.content.length : 0);
      }, 0);

      const averageWordsPerChapter = storyData.chapters.length > 0 ? 
        Math.round(totalWords / storyData.chapters.length) : 0;

      return {
        gameStartDate: gameState.gameStartDate,
        currentDay: gameState.currentDay,
        totalDeliveries: gameState.totalDeliveries,
        totalChapters: storyData.chapters.length,
        totalWords: totalWords,
        averageWordsPerChapter: averageWordsPerChapter,
        itemsDelivered: gameState.playerStats.itemsDelivered,
        gameDuration: await this.getGameDuration(),
        achievements: achievements,
        canDeliverToday: await this.canDeliverToday(),
        nextDeliveryTime: await this.getNextDeliveryTime()
      };
    } catch (error) {
      console.error('Error getting game statistics:', error);
      throw error;
    }
  }
}

module.exports = new GameService();
