const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

class SurvivalStoryTest {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE,
      timeout: 30000
    });
  }

  async testSurvivalStoryGeneration() {
    console.log('🏝️ 测试荒岛生存故事生成...\n');

    const testItems = [
      {
        name: '瑞士军刀',
        description: '一把多功能的瑞士军刀，包含刀片、螺丝刀、开瓶器等多种工具，非常实用'
      },
      {
        name: '防水火柴',
        description: '一盒防水火柴，即使在潮湿环境中也能正常点燃，对生存至关重要'
      },
      {
        name: '钓鱼线',
        description: '一卷结实的钓鱼线，可以用来制作陷阱、缝补衣物或者钓鱼'
      }
    ];

    try {
      for (let i = 0; i < testItems.length; i++) {
        const item = testItems[i];
        console.log(`${i + 1}. 测试物品: ${item.name}`);
        console.log(`   描述: ${item.description}`);

        const delivery = await this.client.post('/delivery', {
          item: item.name,
          description: item.description
        });

        if (delivery.data.success) {
          const chapter = delivery.data.data.chapter;
          const content = chapter.content;

          console.log(`✅ 故事生成成功`);
          console.log(`   天数: 第${chapter.day}天`);
          console.log(`   字数: ${content.length}字`);
          console.log(`   AI生成: ${chapter.aiGenerated ? '是' : '否'}`);
          
          // 分析故事内容质量
          this.analyzeStoryContent(content, item.name);
          
          console.log(`   故事预览: ${content.substring(0, 150)}...`);
          console.log('');
        } else {
          console.log(`❌ 故事生成失败: ${delivery.data.error}`);
        }

        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 获取完整故事并分析
      await this.analyzeFullStory();

      console.log('🎉 荒岛生存故事测试完成！');
      return true;

    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      if (error.response) {
        console.error('错误详情:', error.response.data);
      }
      return false;
    }
  }

  analyzeStoryContent(content, itemName) {
    const checks = {
      包含物品名称: content.includes(itemName),
      避免等待包裹: !content.includes('包裹') && !content.includes('投递点'),
      体现生存元素: content.includes('生存') || content.includes('食物') || content.includes('庇护') || content.includes('工具'),
      描述使用过程: content.includes('使用') || content.includes('利用') || content.includes('制作'),
      环境描写: content.includes('海滩') || content.includes('阳光') || content.includes('海风') || content.includes('荒岛'),
      心理描写: content.includes('想') || content.includes('感') || content.includes('心') || content.includes('希望'),
      发现方式合理: content.includes('发现') || content.includes('找到') || content.includes('冲上') || content.includes('探索'),
      生存技能: content.includes('技能') || content.includes('经验') || content.includes('智慧') || content.includes('创造')
    };

    console.log('   内容分析:');
    Object.entries(checks).forEach(([key, value]) => {
      console.log(`     ${value ? '✅' : '❌'} ${key}`);
    });

    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    console.log(`   质量评分: ${passedChecks}/${totalChecks} (${Math.round(passedChecks/totalChecks*100)}%)`);
  }

  async analyzeFullStory() {
    console.log('📖 分析完整故事...');

    try {
      const story = await this.client.get('/story');
      const chapters = story.data.data.chapters;

      console.log(`总章节数: ${chapters.length}`);
      console.log(`当前天数: ${story.data.data.currentDay}`);

      let totalWords = 0;
      let survivalChapters = 0;

      chapters.forEach((chapter, index) => {
        if (chapter.deliveredItem) {
          totalWords += chapter.content.length;
          
          // 检查是否体现生存主题
          const isSurvivalFocused = 
            chapter.content.includes('生存') ||
            chapter.content.includes('工具') ||
            chapter.content.includes('食物') ||
            chapter.content.includes('庇护') ||
            chapter.content.includes('技能');
          
          if (isSurvivalFocused) {
            survivalChapters++;
          }

          console.log(`第${chapter.day}天 - ${chapter.deliveredItem.name}: ${chapter.content.length}字 ${isSurvivalFocused ? '🏝️' : '❓'}`);
        }
      });

      const avgWords = Math.round(totalWords / chapters.filter(ch => ch.deliveredItem).length);
      const survivalRate = Math.round(survivalChapters / chapters.filter(ch => ch.deliveredItem).length * 100);

      console.log(`平均字数: ${avgWords}字`);
      console.log(`生存主题比例: ${survivalRate}%`);

      if (avgWords >= 400 && survivalRate >= 80) {
        console.log('✅ 故事质量优秀！');
      } else if (avgWords >= 300 && survivalRate >= 60) {
        console.log('⚠️ 故事质量良好，还有改进空间');
      } else {
        console.log('❌ 故事质量需要改进');
      }

    } catch (error) {
      console.error('分析完整故事失败:', error.message);
    }
  }

  async testSpecificSurvivalScenarios() {
    console.log('\n🔧 测试特定生存场景...');

    const scenarios = [
      {
        name: '净水器',
        description: '一个便携式净水器，可以将海水或污水净化成可饮用的淡水',
        expectedElements: ['淡水', '净化', '饮用', '生存']
      },
      {
        name: '渔网',
        description: '一张结实的渔网，可以用来捕鱼或者制作陷阱',
        expectedElements: ['捕鱼', '陷阱', '食物', '蛋白质']
      },
      {
        name: '太阳能充电器',
        description: '一个小型太阳能充电器，可以为电子设备充电',
        expectedElements: ['太阳能', '充电', '电子设备', '信号']
      }
    ];

    for (const scenario of scenarios) {
      console.log(`\n测试场景: ${scenario.name}`);
      
      try {
        const delivery = await this.client.post('/delivery', {
          item: scenario.name,
          description: scenario.description
        });

        if (delivery.data.success) {
          const content = delivery.data.data.chapter.content;
          const foundElements = scenario.expectedElements.filter(element => 
            content.toLowerCase().includes(element.toLowerCase())
          );

          console.log(`✅ 生成成功，包含预期元素: ${foundElements.length}/${scenario.expectedElements.length}`);
          console.log(`   包含: ${foundElements.join(', ')}`);
          
          if (foundElements.length >= scenario.expectedElements.length * 0.5) {
            console.log('✅ 场景测试通过');
          } else {
            console.log('⚠️ 场景相关性有待提高');
          }
        }
      } catch (error) {
        console.log(`❌ 场景测试失败: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

async function main() {
  const tester = new SurvivalStoryTest();

  console.log('🚀 开始荒岛生存故事测试...\n');

  try {
    const basicTest = await tester.testSurvivalStoryGeneration();
    await tester.testSpecificSurvivalScenarios();

    if (basicTest) {
      console.log('\n🎉 测试完成！新的生存故事机制工作正常');
      console.log('改进要点:');
      console.log('- 不再依赖神秘包裹的设定');
      console.log('- 重点描述真实的荒岛生存');
      console.log('- 展现物品的实际生存价值');
      console.log('- 体现主角的生存智慧和技能');
    } else {
      console.log('\n⚠️ 测试发现问题，需要进一步调整');
    }

  } catch (error) {
    console.error('测试运行失败:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().then(() => {
    console.log('\n测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = SurvivalStoryTest;
