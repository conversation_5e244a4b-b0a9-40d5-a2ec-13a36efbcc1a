<template>
  <div class="delivery">
    <!-- 投递状态卡片 -->
    <el-card class="status-card" shadow="hover">
      <div class="delivery-status">
        <div class="status-info">
          <h2>投递状态</h2>
          <div class="status-details">
            <div class="status-item">
              <i class="el-icon-calendar"></i>
              <span>第 {{ gameStatus.currentDay || 1 }} 天</span>
            </div>
            <div class="status-item">
              <i class="el-icon-box"></i>
              <span>总投递: {{ gameStatus.totalDeliveries || 0 }} 次</span>
            </div>
            <div class="status-item">
              <i :class="gameStatus.canDeliverToday ? 'el-icon-check' : 'el-icon-close'"></i>
              <span :class="{ 'can-deliver': gameStatus.canDeliverToday, 'cannot-deliver': !gameStatus.canDeliverToday }">
                {{ gameStatus.canDeliverToday ? '今日可投递' : '今日已投递' }}
              </span>
            </div>
          </div>
        </div>
        <div class="status-icon">
          <i class="el-icon-present" :class="{ 'active': gameStatus.canDeliverToday }"></i>
        </div>
      </div>
    </el-card>

    <!-- 投递表单 -->
    <el-card class="delivery-form-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>投递新物品</span>
        <el-tag type="success">投递后立即进入下一天</el-tag>
      </div>
      
      <el-form 
        ref="deliveryForm" 
        :model="deliveryForm" 
        :rules="deliveryRules" 
        label-width="100px"
        @submit.native.prevent="submitDelivery"
      >
        <el-form-item label="物品名称" prop="item">
          <el-input
            v-model="deliveryForm.item"
            placeholder="请输入要投递的物品名称"
            maxlength="50"
            show-word-limit
            clearable
          />
        </el-form-item>
        
        <el-form-item label="物品描述" prop="description">
          <el-input
            type="textarea"
            v-model="deliveryForm.description"
            placeholder="请详细描述这个物品的特点、用途或背景故事..."
            :rows="4"
            maxlength="200"
            show-word-limit
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            @click="submitDelivery"
            :loading="submitting"
            size="large"
            style="width: 100%;"
          >
            <i class="el-icon-position"></i>
            {{ submitting ? '生成故事中...' : '投递物品并生成故事' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>



    <!-- 投递历史 -->
    <el-card class="history-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>投递历史</span>
        <el-button type="text" @click="loadDeliveryHistory">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>
      
      <div v-if="deliveryHistory.length > 0">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in deliveryHistory.slice(0, 5)"
            :key="index"
            :timestamp="formatTime(item.deliveryDate)"
            type="primary"
            icon="el-icon-box"
          >
            <div class="history-item">
              <h4>{{ item.name }}</h4>
              <p>{{ item.description }}</p>
              <el-tag size="mini">第 {{ item.day }} 天</el-tag>
            </div>
          </el-timeline-item>
        </el-timeline>
        
        <div class="history-more" v-if="deliveryHistory.length > 5">
          <el-button type="text" @click="showAllHistory = !showAllHistory">
            {{ showAllHistory ? '收起' : `查看全部 ${deliveryHistory.length} 条记录` }}
          </el-button>
        </div>
      </div>
      
      <el-empty v-else description="还没有投递记录" />
    </el-card>

    <!-- 投递建议 -->
    <el-card class="suggestions-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>投递建议</span>
        <i class="el-icon-info"></i>
      </div>
      
      <div class="suggestions">
        <el-alert
          title="投递小贴士"
          type="info"
          :closable="false"
          show-icon
        >
          <ul>
            <li>物品名称要简洁明了，便于AI理解</li>
            <li>详细的描述能让故事更加精彩丰富</li>
            <li>可以投递任何你能想象到的物品</li>
            <li>每次投递都会生成详细的故事章节</li>
            <li>投递后立即进入下一天，可以连续投递</li>
            <li>每个物品都会深度影响主角的荒岛生活</li>
          </ul>
        </el-alert>
        
        <div class="example-items">
          <h4>示例物品：</h4>
          <el-tag 
            v-for="example in exampleItems" 
            :key="example"
            @click="fillExample(example)"
            style="margin: 5px; cursor: pointer;"
            type="info"
          >
            {{ example }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Delivery',
  data() {
    return {
      gameStatus: {},
      deliveryForm: {
        item: '',
        description: ''
      },
      deliveryRules: {
        item: [
          { required: true, message: '请输入物品名称', trigger: 'blur' },
          { min: 1, max: 50, message: '物品名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入物品描述', trigger: 'blur' },
          { min: 10, max: 200, message: '物品描述长度在 10 到 200 个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      deliveryHistory: [],
      showAllHistory: false,
      exampleItems: [
        '瑞士军刀', '太阳能充电器', '种子包', '钓鱼竿', 
        '医疗包', '望远镜', '打火石', '净水器',
        '帐篷', '指南针', '收音机', '绳索'
      ]
    }
  },
  async created() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadGameStatus(),
        this.loadDeliveryHistory()
      ])
    },
    
    async loadGameStatus() {
      try {
        const response = await this.$http.get('/game/status')
        if (response.data.success) {
          this.gameStatus = response.data.data
          this.$emit('game-status-updated', this.gameStatus)
        }
      } catch (error) {
        console.error('Failed to load game status:', error)
      }
    },
    
    async loadDeliveryHistory() {
      try {
        const response = await this.$http.get('/delivery/history')
        if (response.data.success) {
          this.deliveryHistory = response.data.data.items || []
        }
      } catch (error) {
        console.error('Failed to load delivery history:', error)
      }
    },
    
    async submitDelivery() {
      try {
        await this.$refs.deliveryForm.validate()
        
        this.submitting = true
        
        const response = await this.$http.post('/delivery', {
          item: this.deliveryForm.item.trim(),
          description: this.deliveryForm.description.trim()
        })
        
        if (response.data.success) {
          this.$message.success('物品投递成功！详细故事章节已生成，游戏进入下一天')

          // 重置表单
          this.deliveryForm = { item: '', description: '' }
          this.$refs.deliveryForm.resetFields()

          // 更新状态
          await this.loadData()

          // 跳转到故事页面查看新章节
          this.$router.push('/story')
        }
      } catch (error) {
        if (error.response && error.response.data) {
          this.$message.error(error.response.data.error || '投递失败')
        } else {
          this.$message.error('投递失败，请重试')
        }
      } finally {
        this.submitting = false
      }
    },
    
    fillExample(example) {
      if (this.gameStatus.canDeliverToday) {
        this.deliveryForm.item = example
        this.$refs.deliveryForm.validateField('item')
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.delivery {
  max-width: 800px;
  margin: 0 auto;
}

.status-card {
  margin-bottom: 20px;
}

.delivery-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info h2 {
  color: #303133;
  margin-bottom: 15px;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.status-item i {
  width: 16px;
}

.can-deliver {
  color: #67c23a;
  font-weight: bold;
}

.cannot-deliver {
  color: #f56c6c;
}

.status-icon {
  font-size: 60px;
  color: #ddd;
}

.status-icon .active {
  color: #409eff;
}

.delivery-form-card,
.no-delivery-card,
.history-card,
.suggestions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-delivery-content {
  text-align: center;
  padding: 40px 20px;
}

.no-delivery-content i {
  font-size: 60px;
  color: #f56c6c;
  margin-bottom: 20px;
}

.no-delivery-content h3 {
  color: #303133;
  margin-bottom: 10px;
}

.no-delivery-content p {
  color: #606266;
  margin-bottom: 10px;
}

.next-delivery-time {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 20px !important;
}

.history-item h4 {
  color: #303133;
  margin-bottom: 5px;
}

.history-item p {
  color: #606266;
  margin-bottom: 8px;
}

.history-more {
  text-align: center;
  margin-top: 15px;
}

.suggestions ul {
  margin: 15px 0;
  padding-left: 20px;
}

.suggestions li {
  margin-bottom: 5px;
  color: #606266;
}

.example-items {
  margin-top: 20px;
}

.example-items h4 {
  color: #303133;
  margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .delivery {
    padding: 0 10px;
  }
  
  .delivery-status {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .status-details {
    align-items: center;
  }
}
</style>
