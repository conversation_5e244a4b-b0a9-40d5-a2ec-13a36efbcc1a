const fs = require('fs-extra');
const path = require('path');
const config = require('../config/config');

class DataService {
  constructor() {
    this.dataDir = path.join(__dirname, '..', config.storage.dataDir);
    this.storyFile = path.join(this.dataDir, config.storage.storyFile);
    this.gameStateFile = path.join(this.dataDir, config.storage.gameStateFile);
    
    // 确保数据目录存在
    this.ensureDataDir();
  }

  async ensureDataDir() {
    try {
      await fs.ensureDir(this.dataDir);
    } catch (error) {
      console.error('Error creating data directory:', error);
    }
  }

  // 读取故事数据
  async getStoryData() {
    try {
      const exists = await fs.pathExists(this.storyFile);
      if (!exists) {
        return this.getDefaultStoryData();
      }
      const data = await fs.readJson(this.storyFile);
      return data;
    } catch (error) {
      console.error('Error reading story data:', error);
      return this.getDefaultStoryData();
    }
  }

  // 保存故事数据
  async saveStoryData(storyData) {
    try {
      storyData.lastUpdated = new Date().toISOString();
      await fs.writeJson(this.storyFile, storyData, { spaces: 2 });
      return true;
    } catch (error) {
      console.error('Error saving story data:', error);
      return false;
    }
  }

  // 读取游戏状态
  async getGameState() {
    try {
      const exists = await fs.pathExists(this.gameStateFile);
      if (!exists) {
        return this.getDefaultGameState();
      }
      const data = await fs.readJson(this.gameStateFile);
      return data;
    } catch (error) {
      console.error('Error reading game state:', error);
      return this.getDefaultGameState();
    }
  }

  // 保存游戏状态
  async saveGameState(gameState) {
    try {
      await fs.writeJson(this.gameStateFile, gameState, { spaces: 2 });
      return true;
    } catch (error) {
      console.error('Error saving game state:', error);
      return false;
    }
  }

  // 添加新章节
  async addChapter(chapter) {
    try {
      const storyData = await this.getStoryData();
      storyData.chapters.push(chapter);
      storyData.currentDay = chapter.day;
      return await this.saveStoryData(storyData);
    } catch (error) {
      console.error('Error adding chapter:', error);
      return false;
    }
  }

  // 获取默认故事数据
  getDefaultStoryData() {
    return {
      title: "荒岛求生记",
      chapters: [
        {
          day: 0,
          title: "序章：意外降临",
          content: "李明原本是一名普通的上班族，在一次商务旅行中遭遇了飞机失事。当他从昏迷中醒来时，发现自己躺在一个陌生的海滩上...",
          deliveredItem: null,
          timestamp: new Date().toISOString()
        }
      ],
      currentDay: 1,
      lastUpdated: new Date().toISOString()
    };
  }

  // 获取默认游戏状态
  getDefaultGameState() {
    return {
      currentDay: 1,
      lastDeliveryDate: null,
      canDeliverToday: true,
      totalDeliveries: 0,
      gameStartDate: new Date().toISOString(),
      playerStats: {
        itemsDelivered: [],
        storyProgress: 1
      }
    };
  }
}

module.exports = new DataService();
