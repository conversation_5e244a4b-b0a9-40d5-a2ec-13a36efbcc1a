<template>
  <div class="stats">
    <!-- 游戏统计概览 -->
    <el-card class="overview-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>游戏统计概览</span>
        <el-button type="text" @click="loadData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ gameStats.currentDay || 0 }}</div>
            <div class="stat-label">当前天数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ gameStats.totalDeliveries || 0 }}</div>
            <div class="stat-label">总投递次数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ storyStats.totalChapters || 0 }}</div>
            <div class="stat-label">故事章节</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ storyStats.totalWords || 0 }}</div>
            <div class="stat-label">总字数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 游戏进度 -->
    <el-card class="progress-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>游戏进度</span>
      </div>
      
      <div class="progress-content">
        <div class="progress-info">
          <div class="info-item">
            <span class="info-label">游戏开始时间：</span>
            <span class="info-value">{{ formatTime(gameStats.gameStartDate) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">游戏持续时间：</span>
            <span class="info-value">{{ gameStats.gameDuration || 0 }} 天</span>
          </div>
          <div class="info-item">
            <span class="info-label">平均每章字数：</span>
            <span class="info-value">{{ gameStats.averageWordsPerChapter || 0 }} 字</span>
          </div>
        </div>
        
        <div class="progress-visual">
          <el-progress 
            :percentage="Math.min((gameStats.currentDay || 0) * 10, 100)" 
            :stroke-width="20"
            :text-inside="true"
            status="success"
          />
          <p class="progress-text">荒岛生存进度</p>
        </div>
      </div>
    </el-card>

    <!-- 投递物品统计 -->
    <el-card class="items-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>投递物品统计</span>
        <el-tag type="info">共 {{ deliveredItems.length }} 件物品</el-tag>
      </div>
      
      <div v-if="deliveredItems.length > 0">
        <div class="items-grid">
          <div 
            v-for="(item, index) in deliveredItems" 
            :key="index"
            class="item-card"
          >
            <div class="item-header">
              <h4 class="item-name">{{ item.name }}</h4>
              <el-tag size="mini">第{{ item.day }}天</el-tag>
            </div>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-footer">
              <span class="item-date">{{ formatTime(item.deliveryDate) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <el-empty v-else description="还没有投递任何物品" />
    </el-card>

    <!-- 故事统计 -->
    <el-card class="story-stats-card" shadow="hover">
      <div slot="header" class="card-header">
        <span>故事统计</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <div class="chart-container">
            <h4>章节字数分布</h4>
            <div class="simple-chart">
              <div 
                v-for="(chapter, index) in recentChapters" 
                :key="index"
                class="chart-bar"
              >
                <div class="bar-label">第{{ chapter.day }}天</div>
                <div class="bar-container">
                  <div 
                    class="bar-fill" 
                    :style="{ width: getBarWidth(chapter.content.length) + '%' }"
                  ></div>
                </div>
                <div class="bar-value">{{ chapter.content.length }}字</div>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12">
          <div class="achievements">
            <h4>成就系统</h4>
            <div class="achievement-list">
              <div 
                v-for="achievement in achievements" 
                :key="achievement.id"
                class="achievement-item"
                :class="{ 'unlocked': achievement.unlocked }"
              >
                <i :class="achievement.icon"></i>
                <div class="achievement-info">
                  <div class="achievement-name">{{ achievement.name }}</div>
                  <div class="achievement-desc">{{ achievement.description }}</div>
                </div>
                <div class="achievement-status">
                  <i v-if="achievement.unlocked" class="el-icon-check"></i>
                  <i v-else class="el-icon-lock"></i>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button type="danger" @click="showResetDialog = true">
        <i class="el-icon-refresh-left"></i>
        重置游戏
      </el-button>
    </div>

    <!-- 重置确认对话框 -->
    <el-dialog
      title="重置游戏"
      :visible.sync="showResetDialog"
      width="400px"
      center
    >
      <div class="reset-dialog-content">
        <i class="el-icon-warning" style="color: #f56c6c; font-size: 48px;"></i>
        <p>确定要重置游戏吗？</p>
        <p style="color: #909399; font-size: 14px;">这将删除所有故事内容和投递记录，且无法恢复！</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showResetDialog = false">取消</el-button>
        <el-button type="danger" @click="resetGame" :loading="resetting">
          确定重置
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Stats',
  data() {
    return {
      loading: false,
      gameStats: {},
      storyStats: {},
      deliveredItems: [],
      recentChapters: [],
      showResetDialog: false,
      resetting: false,
      achievements: [
        {
          id: 1,
          name: '初来乍到',
          description: '投递第一个物品',
          icon: 'el-icon-box',
          unlocked: false
        },
        {
          id: 2,
          name: '生存专家',
          description: '生存超过7天',
          icon: 'el-icon-trophy',
          unlocked: false
        },
        {
          id: 3,
          name: '物资丰富',
          description: '投递超过10个物品',
          icon: 'el-icon-present',
          unlocked: false
        },
        {
          id: 4,
          name: '故事大师',
          description: '故事总字数超过5000字',
          icon: 'el-icon-edit',
          unlocked: false
        }
      ]
    }
  },
  async created() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadGameStats(),
          this.loadStoryStats(),
          this.loadStoryData()
        ])
        this.updateAchievements()
      } catch (error) {
        console.error('Failed to load stats:', error)
      } finally {
        this.loading = false
      }
    },
    
    async loadGameStats() {
      try {
        const response = await this.$http.get('/game/statistics')
        if (response.data.success) {
          this.gameStats = response.data.data
          this.deliveredItems = response.data.data.itemsDelivered || []
        }
      } catch (error) {
        console.error('Failed to load game stats:', error)
      }
    },
    
    async loadStoryStats() {
      try {
        const response = await this.$http.get('/story/stats')
        if (response.data.success) {
          this.storyStats = response.data.data
        }
      } catch (error) {
        console.error('Failed to load story stats:', error)
      }
    },
    
    async loadStoryData() {
      try {
        const response = await this.$http.get('/story')
        if (response.data.success) {
          this.recentChapters = response.data.data.chapters.slice(-5) || []
        }
      } catch (error) {
        console.error('Failed to load story data:', error)
      }
    },
    
    async resetGame() {
      this.resetting = true
      try {
        const response = await this.$http.post('/game/reset')
        if (response.data.success) {
          this.$message.success('游戏重置成功！')
          this.showResetDialog = false
          await this.loadData()
          this.$router.push('/')
        }
      } catch (error) {
        this.$message.error('重置失败，请重试')
      } finally {
        this.resetting = false
      }
    },
    
    updateAchievements() {
      // 更新成就状态
      this.achievements[0].unlocked = this.gameStats.totalDeliveries > 0
      this.achievements[1].unlocked = this.gameStats.currentDay > 7
      this.achievements[2].unlocked = this.gameStats.totalDeliveries > 10
      this.achievements[3].unlocked = this.storyStats.totalWords > 5000
    },
    
    getBarWidth(wordCount) {
      const maxWords = Math.max(...this.recentChapters.map(ch => ch.content.length))
      return maxWords > 0 ? (wordCount / maxWords) * 100 : 0
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '未知'
      return new Date(timestamp).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.stats {
  max-width: 1000px;
  margin: 0 auto;
}

.overview-card,
.progress-card,
.items-card,
.story-stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.progress-content {
  display: flex;
  gap: 30px;
  align-items: center;
}

.progress-info {
  flex: 1;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  color: #909399;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.progress-visual {
  flex: 1;
  text-align: center;
}

.progress-text {
  margin-top: 10px;
  color: #606266;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.item-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-name {
  color: #303133;
  margin: 0;
}

.item-description {
  color: #606266;
  margin-bottom: 10px;
  font-size: 14px;
}

.item-footer {
  text-align: right;
}

.item-date {
  color: #909399;
  font-size: 12px;
}

.chart-container h4,
.achievements h4 {
  color: #303133;
  margin-bottom: 15px;
}

.simple-chart {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chart-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bar-label {
  min-width: 60px;
  font-size: 12px;
  color: #606266;
}

.bar-container {
  flex: 1;
  height: 20px;
  background-color: #f5f7fa;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: width 0.3s ease;
}

.bar-value {
  min-width: 50px;
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  opacity: 0.6;
}

.achievement-item.unlocked {
  background-color: #f0f9ff;
  opacity: 1;
}

.achievement-item i:first-child {
  font-size: 24px;
  color: #909399;
}

.achievement-item.unlocked i:first-child {
  color: #409eff;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.achievement-desc {
  font-size: 12px;
  color: #606266;
}

.achievement-status i {
  font-size: 18px;
  color: #909399;
}

.achievement-item.unlocked .achievement-status i {
  color: #67c23a;
}

.actions {
  text-align: center;
  padding: 20px 0;
}

.reset-dialog-content {
  text-align: center;
  padding: 20px 0;
}

.reset-dialog-content p {
  margin: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats {
    padding: 0 10px;
  }
  
  .progress-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .items-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .bar-container {
    width: 100%;
  }
}
</style>
