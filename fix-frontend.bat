@echo off
echo 修复前端依赖问题...
echo ========================

cd frontend

echo 清理npm缓存...
npm cache clean --force

echo 删除node_modules和package-lock.json...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

echo 修复vue.config.js配置...
echo 已修复defineConfig问题

echo 使用--legacy-peer-deps安装依赖...
npm install --legacy-peer-deps

if errorlevel 1 (
    echo 尝试使用--force参数...
    npm install --force
)

if errorlevel 1 (
    echo 尝试使用淘宝镜像...
    npm install --registry https://registry.npmmirror.com --legacy-peer-deps
)

echo 测试启动服务...
echo 如果出现错误，请查看错误信息

cd ..
echo.
echo 前端依赖修复完成！
echo 现在可以运行: cd frontend && npm run serve
echo 或者直接运行启动脚本
pause
