{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\App.vue?vue&type=template&id=7ba5bd90", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\App.vue", "mtime": 1756199840298}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}