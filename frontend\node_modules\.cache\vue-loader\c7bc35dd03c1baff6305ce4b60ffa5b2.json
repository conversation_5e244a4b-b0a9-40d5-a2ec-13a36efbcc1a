{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue?vue&type=template&id=5031b956&scoped=true", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue", "mtime": 1756200035137}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}