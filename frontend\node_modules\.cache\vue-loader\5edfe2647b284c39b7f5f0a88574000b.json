{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\App.vue", "mtime": 1756199840298}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZ2FtZVN0YXR1czogbnVsbAogICAgfQogIH0sCiAgYXN5bmMgY3JlYXRlZCgpIHsKICAgIGF3YWl0IHRoaXMubG9hZEdhbWVTdGF0dXMoKQogIH0sCiAgbWV0aG9kczogewogICAgYXN5bmMgbG9hZEdhbWVTdGF0dXMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLiRodHRwLmdldCgnL2dhbWUvc3RhdHVzJykKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7CiAgICAgICAgICB0aGlzLmdhbWVTdGF0dXMgPSByZXNwb25zZS5kYXRhLmRhdGEKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgZ2FtZSBzdGF0dXM6JywgZXJyb3IpCiAgICAgIH0KICAgIH0sCiAgICB1cGRhdGVHYW1lU3RhdHVzKG5ld1N0YXR1cykgewogICAgICB0aGlzLmdhbWVTdGF0dXMgPSB7IC4uLnRoaXMuZ2FtZVN0YXR1cywgLi4ubmV3U3RhdHVzIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAyDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <el-container class=\"app-container\">\n      <!-- 头部 -->\n      <el-header class=\"app-header\">\n        <div class=\"header-content\">\n          <h1 class=\"app-title\">\n            <i class=\"el-icon-reading\"></i>\n            AI小说生成器 - 荒岛求生记\n          </h1>\n          <div class=\"game-status\">\n            <el-tag v-if=\"gameStatus\" :type=\"gameStatus.canDeliverToday ? 'success' : 'info'\">\n              第 {{ gameStatus.currentDay }} 天\n            </el-tag>\n            <el-tag v-if=\"gameStatus\" :type=\"gameStatus.canDeliverToday ? 'primary' : 'warning'\" style=\"margin-left: 10px;\">\n              {{ gameStatus.canDeliverToday ? '可以投递' : '今日已投递' }}\n            </el-tag>\n          </div>\n        </div>\n      </el-header>\n\n      <!-- 主体内容 -->\n      <el-main class=\"app-main\">\n        <router-view @game-status-updated=\"updateGameStatus\" />\n      </el-main>\n\n      <!-- 底部导航 -->\n      <el-footer class=\"app-footer\">\n        <el-menu\n          :default-active=\"$route.path\"\n          mode=\"horizontal\"\n          router\n          class=\"footer-menu\"\n        >\n          <el-menu-item index=\"/\">\n            <i class=\"el-icon-house\"></i>\n            <span>首页</span>\n          </el-menu-item>\n          <el-menu-item index=\"/story\">\n            <i class=\"el-icon-notebook-1\"></i>\n            <span>故事</span>\n          </el-menu-item>\n          <el-menu-item index=\"/delivery\">\n            <i class=\"el-icon-box\"></i>\n            <span>投递</span>\n          </el-menu-item>\n          <el-menu-item index=\"/stats\">\n            <i class=\"el-icon-data-analysis\"></i>\n            <span>统计</span>\n          </el-menu-item>\n        </el-menu>\n      </el-footer>\n    </el-container>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App',\n  data() {\n    return {\n      gameStatus: null\n    }\n  },\n  async created() {\n    await this.loadGameStatus()\n  },\n  methods: {\n    async loadGameStatus() {\n      try {\n        const response = await this.$http.get('/game/status')\n        if (response.data.success) {\n          this.gameStatus = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load game status:', error)\n      }\n    },\n    updateGameStatus(newStatus) {\n      this.gameStatus = { ...this.gameStatus, ...newStatus }\n    }\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: 'Avenir', Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  height: 100vh;\n}\n\n.app-container {\n  height: 100vh;\n}\n\n.app-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 100%;\n}\n\n.app-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.app-title i {\n  margin-right: 10px;\n}\n\n.game-status {\n  display: flex;\n  align-items: center;\n}\n\n.app-main {\n  background-color: #f5f7fa;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.app-footer {\n  height: auto !important;\n  padding: 0;\n  border-top: 1px solid #e4e7ed;\n}\n\n.footer-menu {\n  border: none;\n}\n\n.footer-menu .el-menu-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 10px 20px;\n  min-width: 80px;\n}\n\n.footer-menu .el-menu-item i {\n  font-size: 18px;\n  margin-bottom: 4px;\n}\n\n.footer-menu .el-menu-item span {\n  font-size: 12px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .app-title {\n    font-size: 20px;\n    margin-bottom: 10px;\n  }\n  \n  .app-main {\n    padding: 15px;\n  }\n  \n  .footer-menu .el-menu-item {\n    padding: 8px 15px;\n    min-width: 60px;\n  }\n}\n</style>\n"]}]}