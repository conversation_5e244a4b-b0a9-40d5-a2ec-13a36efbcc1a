@echo off
echo 启动前端服务...
echo ==================

echo 检查Node.js版本...
node --version

echo 进入前端目录...
cd frontend

echo 检查依赖...
if not exist node_modules (
    echo 安装前端依赖...
    npm install --legacy-peer-deps
    if errorlevel 1 (
        echo 依赖安装失败，尝试其他方法...
        npm install --force
    )
)

echo 检查配置文件...
if exist vue.config.js (
    echo vue.config.js 存在
) else (
    echo 创建基本的 vue.config.js...
    echo module.exports = { > vue.config.js
    echo   devServer: { >> vue.config.js
    echo     port: 8080 >> vue.config.js
    echo   } >> vue.config.js
    echo } >> vue.config.js
)

echo 启动开发服务器...
npm run serve

cd ..
pause
