# AI小说生成器 - 部署指南

## 系统要求

### 基础环境
- Node.js 16.0 或更高版本
- npm 8.0 或更高版本
- 操作系统：Windows 10+、macOS 10.15+、Ubuntu 18.04+

### API服务
- DeepSeek API Key（可选，没有则使用备用内容生成）

## 快速开始

### 1. 下载项目
```bash
git clone <repository-url>
cd xiaoshuo_generator
```

### 2. 一键启动（推荐）

#### Windows用户
双击运行 `start.bat` 文件，或在命令行中执行：
```cmd
start.bat
```

#### Linux/Mac用户
```bash
chmod +x start.sh
./start.sh
```

### 3. 手动启动

#### 启动后端服务
```bash
cd backend
npm install
cp .env.example .env
# 编辑 .env 文件，设置 DEEPSEEK_API_KEY
npm start
```

#### 启动前端服务
```bash
cd frontend
npm install
npm run serve
```

## 配置说明

### 环境变量配置
编辑 `backend/.env` 文件：

```env
# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 服务器配置
PORT=3000
HOST=localhost

# 开发环境配置
NODE_ENV=development
```

### DeepSeek API Key获取
1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在API管理页面创建新的API Key
4. 将API Key复制到 `.env` 文件中

**注意：** 如果没有API Key，系统会自动使用备用内容生成功能，不影响基本使用。

## 访问应用

启动成功后，在浏览器中访问：
- 前端应用：http://localhost:8080
- 后端API：http://localhost:3000

## 功能测试

### 1. 基础功能测试
- 访问首页，查看游戏状态
- 尝试投递一个物品
- 查看生成的故事章节
- 查看统计信息

### 2. API测试
运行后端测试脚本：
```bash
cd backend
node test/api-test.js
```

### 3. AI服务测试
```bash
cd backend
node test/ai-service-test.js
```

## 常见问题

### Q: 启动时提示"未找到Node.js"
A: 请先安装Node.js，下载地址：https://nodejs.org/

### Q: 前端页面无法访问后端API
A: 检查后端服务是否正常启动，确认端口3000没有被占用

### Q: AI生成的内容是备用内容
A: 检查DeepSeek API Key是否正确配置，或者API服务是否可用

### Q: 投递物品后没有生成新章节
A: 检查后端日志，可能是AI服务调用失败，系统会自动使用备用内容

### Q: 数据丢失了怎么办
A: 游戏数据保存在 `backend/data/` 目录下，可以备份这个目录

## 生产环境部署

### 1. 构建前端
```bash
cd frontend
npm run build
```

### 2. 配置反向代理
使用Nginx配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 使用PM2管理进程
```bash
npm install -g pm2

# 启动后端服务
cd backend
pm2 start server.js --name "xiaoshuo-backend"

# 查看状态
pm2 status

# 查看日志
pm2 logs xiaoshuo-backend
```

## 数据备份

### 备份游戏数据
```bash
# 备份数据目录
cp -r backend/data backend/data_backup_$(date +%Y%m%d_%H%M%S)
```

### 恢复游戏数据
```bash
# 恢复数据
cp -r backend/data_backup_YYYYMMDD_HHMMSS backend/data
```

## 开发说明

### 项目结构
```
xiaoshuo_generator/
├── backend/           # 后端API服务
│   ├── config/       # 配置文件
│   ├── data/         # 数据存储
│   ├── routes/       # 路由定义
│   ├── services/     # 业务逻辑
│   └── test/         # 测试文件
├── frontend/         # 前端Vue应用
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   └── router/
│   └── public/
└── docs/            # 文档
```

### 开发模式启动
```bash
# 后端开发模式
cd backend
npm run dev

# 前端开发模式
cd frontend
npm run serve
```

### 代码规范
- 后端使用ESLint + Prettier
- 前端使用Vue官方代码规范
- 提交前请运行测试用例

## 技术支持

如果遇到问题，请检查：
1. Node.js版本是否符合要求
2. 网络连接是否正常
3. 端口是否被占用
4. API Key是否正确配置

更多技术细节请参考项目源码和注释。
