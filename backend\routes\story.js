const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');

// 获取完整故事
router.get('/', async (req, res) => {
  try {
    const storyData = await dataService.getStoryData();
    res.json({
      success: true,
      data: storyData
    });
  } catch (error) {
    console.error('Error fetching story:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch story data'
    });
  }
});

// 获取特定章节
router.get('/chapter/:day', async (req, res) => {
  try {
    const day = parseInt(req.params.day);
    const storyData = await dataService.getStoryData();
    
    const chapter = storyData.chapters.find(ch => ch.day === day);
    
    if (!chapter) {
      return res.status(404).json({
        success: false,
        error: 'Chapter not found'
      });
    }

    res.json({
      success: true,
      data: chapter
    });
  } catch (error) {
    console.error('Error fetching chapter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chapter'
    });
  }
});

// 获取最新章节
router.get('/latest', async (req, res) => {
  try {
    const storyData = await dataService.getStoryData();
    
    if (!storyData.chapters || storyData.chapters.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No chapters found'
      });
    }

    const latestChapter = storyData.chapters[storyData.chapters.length - 1];
    
    res.json({
      success: true,
      data: latestChapter
    });
  } catch (error) {
    console.error('Error fetching latest chapter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch latest chapter'
    });
  }
});

// 获取故事统计信息
router.get('/stats', async (req, res) => {
  try {
    const storyData = await dataService.getStoryData();
    
    const stats = {
      totalChapters: storyData.chapters.length,
      currentDay: storyData.currentDay,
      totalWords: storyData.chapters.reduce((total, chapter) => {
        return total + (chapter.content ? chapter.content.length : 0);
      }, 0),
      lastUpdated: storyData.lastUpdated,
      deliveredItems: storyData.chapters
        .filter(ch => ch.deliveredItem)
        .map(ch => ch.deliveredItem)
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching story stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch story statistics'
    });
  }
});

module.exports = router;
