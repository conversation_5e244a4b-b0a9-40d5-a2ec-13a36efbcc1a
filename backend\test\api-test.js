const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

class APITester {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE,
      timeout: 30000
    });
  }

  async testHealthCheck() {
    console.log('🏥 测试健康检查...');
    try {
      const response = await this.client.get('/health');
      console.log('✅ 健康检查通过:', response.data);
      return true;
    } catch (error) {
      console.log('❌ 健康检查失败:', error.message);
      return false;
    }
  }

  async testGameStatus() {
    console.log('🎮 测试游戏状态...');
    try {
      const response = await this.client.get('/game/status');
      console.log('✅ 游戏状态:', response.data);
      return response.data.data;
    } catch (error) {
      console.log('❌ 获取游戏状态失败:', error.message);
      return null;
    }
  }

  async testStoryAPI() {
    console.log('📖 测试故事API...');
    try {
      // 测试获取故事
      const storyResponse = await this.client.get('/story');
      console.log('✅ 获取故事成功，章节数:', storyResponse.data.data.chapters.length);

      // 测试获取最新章节
      const latestResponse = await this.client.get('/story/latest');
      console.log('✅ 获取最新章节成功:', latestResponse.data.data.title);

      // 测试获取故事统计
      const statsResponse = await this.client.get('/story/stats');
      console.log('✅ 获取故事统计成功:', statsResponse.data.data);

      return true;
    } catch (error) {
      console.log('❌ 故事API测试失败:', error.message);
      return false;
    }
  }

  async testDeliveryAPI() {
    console.log('📦 测试投递API...');
    try {
      // 测试获取投递状态
      const statusResponse = await this.client.get('/delivery/status');
      console.log('✅ 获取投递状态成功:', statusResponse.data.data);

      // 测试获取投递历史
      const historyResponse = await this.client.get('/delivery/history');
      console.log('✅ 获取投递历史成功，投递次数:', historyResponse.data.data.totalDeliveries);

      // 如果可以投递，测试投递功能
      if (statusResponse.data.data.canDeliverToday) {
        console.log('🚀 测试投递功能...');
        const deliveryResponse = await this.client.post('/delivery', {
          item: '测试物品',
          description: '这是一个用于API测试的物品，具有测试功能，可以验证系统是否正常工作'
        });
        console.log('✅ 投递测试成功:', deliveryResponse.data.message);
        
        // 验证投递后的状态变化
        const newStatusResponse = await this.client.get('/delivery/status');
        console.log('✅ 投递后状态:', newStatusResponse.data.data);
      } else {
        console.log('ℹ️  今日已投递，跳过投递测试');
      }

      return true;
    } catch (error) {
      console.log('❌ 投递API测试失败:', error.message);
      if (error.response) {
        console.log('错误详情:', error.response.data);
      }
      return false;
    }
  }

  async testGameStatistics() {
    console.log('📊 测试游戏统计...');
    try {
      const response = await this.client.get('/game/statistics');
      console.log('✅ 获取游戏统计成功:', response.data.data);
      return true;
    } catch (error) {
      console.log('❌ 游戏统计测试失败:', error.message);
      return false;
    }
  }

  async testErrorHandling() {
    console.log('🚨 测试错误处理...');
    try {
      // 测试无效的投递请求
      try {
        await this.client.post('/delivery', {
          item: '', // 空物品名称
          description: '测试'
        });
        console.log('❌ 应该返回错误但没有');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 正确处理了无效投递请求');
        } else {
          console.log('❌ 错误处理不正确:', error.message);
        }
      }

      // 测试不存在的端点
      try {
        await this.client.get('/nonexistent');
        console.log('❌ 应该返回404但没有');
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log('✅ 正确处理了404错误');
        } else {
          console.log('❌ 404处理不正确:', error.message);
        }
      }

      return true;
    } catch (error) {
      console.log('❌ 错误处理测试失败:', error.message);
      return false;
    }
  }

  async runAllTests() {
    console.log('🧪 开始API测试...\n');
    
    const results = {
      health: false,
      gameStatus: false,
      story: false,
      delivery: false,
      statistics: false,
      errorHandling: false
    };

    // 等待服务器启动
    console.log('⏳ 等待服务器启动...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    results.health = await this.testHealthCheck();
    console.log('');

    if (results.health) {
      results.gameStatus = await this.testGameStatus();
      console.log('');

      results.story = await this.testStoryAPI();
      console.log('');

      results.delivery = await this.testDeliveryAPI();
      console.log('');

      results.statistics = await this.testGameStatistics();
      console.log('');

      results.errorHandling = await this.testErrorHandling();
      console.log('');
    }

    // 输出测试结果
    console.log('📋 测试结果汇总:');
    console.log('================');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASS' : 'FAIL'}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log('');
    console.log(`🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！API服务正常工作');
    } else {
      console.log('⚠️  部分测试失败，请检查服务器状态');
    }

    return results;
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const tester = new APITester();
  tester.runAllTests().then((results) => {
    const allPassed = Object.values(results).every(Boolean);
    process.exit(allPassed ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = APITester;
