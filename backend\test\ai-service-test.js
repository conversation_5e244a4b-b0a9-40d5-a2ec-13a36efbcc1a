const aiService = require('../services/aiService');
const dataService = require('../services/dataService');

async function testAIService() {
  console.log('🧪 开始测试AI服务...\n');

  try {
    // 1. 测试API连接
    console.log('1. 测试API连接状态...');
    const connectionStatus = await aiService.checkConnection();
    console.log('连接状态:', connectionStatus);
    
    if (!connectionStatus.connected) {
      console.log('⚠️  AI服务连接失败，将使用备用内容生成');
    } else {
      console.log('✅ AI服务连接正常');
    }
    console.log('');

    // 2. 测试故事生成
    console.log('2. 测试故事生成...');
    
    // 获取当前故事数据
    const storyData = await dataService.getStoryData();
    console.log('当前故事章节数:', storyData.chapters.length);
    
    // 测试物品
    const testItem = '太阳能充电器';
    const testDescription = '一个小型的太阳能充电器，可以为电子设备充电，在阳光充足的情况下效果很好';
    const testDay = 2;
    
    console.log(`测试物品: ${testItem}`);
    console.log(`物品描述: ${testDescription}`);
    console.log(`故事天数: 第${testDay}天`);
    console.log('');
    
    // 生成故事章节
    console.log('正在生成故事章节...');
    const result = await aiService.generateStoryChapter(
      storyData,
      testItem,
      testDescription,
      testDay
    );
    
    console.log('生成结果:');
    console.log('- 成功:', result.success);
    console.log('- 备用内容:', result.fallback || false);
    if (result.usage) {
      console.log('- Token使用:', result.usage);
    }
    console.log('');
    
    console.log('生成的故事内容:');
    console.log('---');
    console.log(result.content);
    console.log('---');
    console.log('');
    
    // 3. 测试多次生成（验证一致性）
    console.log('3. 测试多次生成（验证变化性）...');
    
    const results = [];
    for (let i = 0; i < 3; i++) {
      console.log(`生成第${i + 1}次...`);
      const testResult = await aiService.generateStoryChapter(
        storyData,
        '神秘的种子',
        '一包来历不明的种子，看起来像是某种热带植物的种子',
        3
      );
      results.push(testResult);
      
      // 显示前100个字符
      console.log(`内容预览: ${testResult.content.substring(0, 100)}...`);
    }
    
    // 检查内容是否有变化
    const uniqueContents = new Set(results.map(r => r.content));
    console.log(`生成了 ${uniqueContents.size} 个不同的内容版本`);
    console.log('');
    
    // 4. 测试错误处理
    console.log('4. 测试错误处理...');
    
    // 测试空物品名称
    try {
      await aiService.generateStoryChapter(storyData, '', '描述', 1);
      console.log('❌ 应该抛出错误但没有');
    } catch (error) {
      console.log('✅ 正确处理了空物品名称错误');
    }
    
    console.log('');
    console.log('🎉 AI服务测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testAIService().then(() => {
    console.log('测试结束');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = { testAIService };
