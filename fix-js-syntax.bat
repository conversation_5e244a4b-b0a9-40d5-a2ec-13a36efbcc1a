@echo off
echo 修复JavaScript语法兼容性问题...
echo ================================

cd frontend

echo 检查并修复可选链操作符问题...

echo 已修复 src/main.js 中的可选链操作符
echo 将 error.response.data?.error 改为 (error.response.data && error.response.data.error)

echo.
echo 检查其他可能的语法问题...

echo 检查 package.json 中的 browserslist 配置...
findstr "browserslist" package.json

echo.
echo 语法修复完成！
echo 现在尝试启动前端服务...

npm run serve

cd ..
pause
