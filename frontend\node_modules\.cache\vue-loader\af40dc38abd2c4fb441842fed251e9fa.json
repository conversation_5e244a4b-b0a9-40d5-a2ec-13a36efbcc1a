{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Story.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Story.vue", "mtime": 1756199925945}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Story.vue"], "names": [], "mappings": ";AA2GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Story.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"story\">\n    <!-- 故事标题 -->\n    <el-card class=\"title-card\" shadow=\"hover\">\n      <div class=\"story-header\">\n        <h1 class=\"story-title\">{{ storyData.title || '荒岛求生记' }}</h1>\n        <div class=\"story-meta\">\n          <el-tag type=\"info\">共 {{ storyData.chapters ? storyData.chapters.length : 0 }} 章</el-tag>\n          <el-tag type=\"success\" style=\"margin-left: 10px;\">\n            第 {{ storyData.currentDay || 1 }} 天\n          </el-tag>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 章节列表 -->\n    <div class=\"chapters-container\" v-loading=\"loading\">\n      <el-timeline v-if=\"storyData.chapters && storyData.chapters.length > 0\">\n        <el-timeline-item\n          v-for=\"(chapter, index) in storyData.chapters\"\n          :key=\"chapter.day\"\n          :timestamp=\"formatTime(chapter.timestamp)\"\n          :type=\"getTimelineType(chapter)\"\n          :icon=\"getTimelineIcon(chapter)\"\n          size=\"large\"\n        >\n          <el-card class=\"chapter-card\" shadow=\"hover\">\n            <div slot=\"header\" class=\"chapter-header\">\n              <span class=\"chapter-title\">{{ chapter.title }}</span>\n              <div class=\"chapter-actions\">\n                <el-tag v-if=\"chapter.deliveredItem\" size=\"mini\" type=\"primary\">\n                  {{ chapter.deliveredItem.name }}\n                </el-tag>\n                <el-button \n                  type=\"text\" \n                  size=\"mini\" \n                  @click=\"toggleChapterExpand(index)\"\n                  :icon=\"expandedChapters.includes(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"\n                >\n                  {{ expandedChapters.includes(index) ? '收起' : '展开' }}\n                </el-button>\n              </div>\n            </div>\n            \n            <div class=\"chapter-content\">\n              <div \n                v-if=\"expandedChapters.includes(index) || chapter.content.length <= 200\"\n                class=\"full-content\"\n              >\n                {{ chapter.content }}\n              </div>\n              <div v-else class=\"preview-content\">\n                {{ chapter.content.substring(0, 200) }}...\n              </div>\n              \n              <!-- 投递物品信息 -->\n              <div v-if=\"chapter.deliveredItem\" class=\"delivered-item\">\n                <el-divider content-position=\"left\">\n                  <i class=\"el-icon-box\"></i>\n                  投递物品\n                </el-divider>\n                <div class=\"item-info\">\n                  <p><strong>物品名称：</strong>{{ chapter.deliveredItem.name }}</p>\n                  <p><strong>物品描述：</strong>{{ chapter.deliveredItem.description }}</p>\n                  <p><strong>投递时间：</strong>{{ formatTime(chapter.deliveredItem.deliveryDate) }}</p>\n                </div>\n              </div>\n              \n              <!-- AI生成标识 -->\n              <div class=\"chapter-footer\">\n                <div class=\"generation-info\">\n                  <el-tag \n                    v-if=\"chapter.aiGenerated !== undefined\" \n                    :type=\"chapter.aiGenerated ? 'success' : 'warning'\"\n                    size=\"mini\"\n                  >\n                    {{ chapter.aiGenerated ? 'AI生成' : '备用内容' }}\n                  </el-tag>\n                </div>\n                <div class=\"chapter-day\">\n                  第 {{ chapter.day }} 天\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-timeline-item>\n      </el-timeline>\n      \n      <!-- 空状态 -->\n      <el-empty v-else description=\"还没有故事章节\" />\n    </div>\n\n    <!-- 操作按钮 -->\n    <div class=\"story-actions\">\n      <el-button type=\"primary\" @click=\"refreshStory\" :loading=\"loading\">\n        <i class=\"el-icon-refresh\"></i>\n        刷新故事\n      </el-button>\n      <el-button type=\"success\" @click=\"$router.push('/delivery')\">\n        <i class=\"el-icon-box\"></i>\n        投递物品\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Story',\n  data() {\n    return {\n      storyData: {},\n      loading: false,\n      expandedChapters: []\n    }\n  },\n  async created() {\n    await this.loadStory()\n  },\n  methods: {\n    async loadStory() {\n      this.loading = true\n      try {\n        const response = await this.$http.get('/story')\n        if (response.data.success) {\n          this.storyData = response.data.data\n          // 默认展开最新的章节\n          if (this.storyData.chapters && this.storyData.chapters.length > 0) {\n            this.expandedChapters = [this.storyData.chapters.length - 1]\n          }\n        }\n      } catch (error) {\n        console.error('Failed to load story:', error)\n        this.$message.error('加载故事失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async refreshStory() {\n      await this.loadStory()\n      this.$message.success('故事已刷新')\n    },\n    \n    toggleChapterExpand(index) {\n      const expandedIndex = this.expandedChapters.indexOf(index)\n      if (expandedIndex > -1) {\n        this.expandedChapters.splice(expandedIndex, 1)\n      } else {\n        this.expandedChapters.push(index)\n      }\n    },\n    \n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleString('zh-CN')\n    },\n    \n    getTimelineType(chapter) {\n      if (chapter.day === 0) return 'info'\n      if (chapter.deliveredItem) return 'primary'\n      return 'success'\n    },\n    \n    getTimelineIcon(chapter) {\n      if (chapter.day === 0) return 'el-icon-star-on'\n      if (chapter.deliveredItem) return 'el-icon-box'\n      return 'el-icon-edit'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.story {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.title-card {\n  margin-bottom: 20px;\n}\n\n.story-header {\n  text-align: center;\n}\n\n.story-title {\n  color: #303133;\n  margin-bottom: 15px;\n  font-size: 28px;\n}\n\n.story-meta {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.chapters-container {\n  margin-bottom: 20px;\n}\n\n.chapter-card {\n  margin-bottom: 0;\n}\n\n.chapter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-title {\n  font-weight: bold;\n  color: #303133;\n}\n\n.chapter-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.chapter-content {\n  line-height: 1.8;\n  color: #606266;\n}\n\n.full-content,\n.preview-content {\n  white-space: pre-wrap;\n  margin-bottom: 15px;\n}\n\n.delivered-item {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin: 15px 0;\n}\n\n.item-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.chapter-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #ebeef5;\n}\n\n.chapter-day {\n  font-size: 12px;\n  color: #909399;\n}\n\n.story-actions {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.story-actions .el-button {\n  margin: 0 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .story {\n    padding: 0 10px;\n  }\n  \n  .story-title {\n    font-size: 24px;\n  }\n  \n  .chapter-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n  \n  .chapter-actions {\n    width: 100%;\n    justify-content: space-between;\n  }\n  \n  .story-actions .el-button {\n    display: block;\n    width: 100%;\n    margin: 10px 0;\n  }\n}\n</style>\n"]}]}