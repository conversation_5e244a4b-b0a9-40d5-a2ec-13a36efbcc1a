{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Story.vue?vue&type=style&index=0&id=8d2d86aa&scoped=true&lang=css", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Story.vue", "mtime": 1756199925945}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756201380880}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756201382571}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756201381511}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Story.vue"], "names": [], "mappings": ";AA6KA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Story.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"story\">\n    <!-- 故事标题 -->\n    <el-card class=\"title-card\" shadow=\"hover\">\n      <div class=\"story-header\">\n        <h1 class=\"story-title\">{{ storyData.title || '荒岛求生记' }}</h1>\n        <div class=\"story-meta\">\n          <el-tag type=\"info\">共 {{ storyData.chapters ? storyData.chapters.length : 0 }} 章</el-tag>\n          <el-tag type=\"success\" style=\"margin-left: 10px;\">\n            第 {{ storyData.currentDay || 1 }} 天\n          </el-tag>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 章节列表 -->\n    <div class=\"chapters-container\" v-loading=\"loading\">\n      <el-timeline v-if=\"storyData.chapters && storyData.chapters.length > 0\">\n        <el-timeline-item\n          v-for=\"(chapter, index) in storyData.chapters\"\n          :key=\"chapter.day\"\n          :timestamp=\"formatTime(chapter.timestamp)\"\n          :type=\"getTimelineType(chapter)\"\n          :icon=\"getTimelineIcon(chapter)\"\n          size=\"large\"\n        >\n          <el-card class=\"chapter-card\" shadow=\"hover\">\n            <div slot=\"header\" class=\"chapter-header\">\n              <span class=\"chapter-title\">{{ chapter.title }}</span>\n              <div class=\"chapter-actions\">\n                <el-tag v-if=\"chapter.deliveredItem\" size=\"mini\" type=\"primary\">\n                  {{ chapter.deliveredItem.name }}\n                </el-tag>\n                <el-button \n                  type=\"text\" \n                  size=\"mini\" \n                  @click=\"toggleChapterExpand(index)\"\n                  :icon=\"expandedChapters.includes(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"\n                >\n                  {{ expandedChapters.includes(index) ? '收起' : '展开' }}\n                </el-button>\n              </div>\n            </div>\n            \n            <div class=\"chapter-content\">\n              <div \n                v-if=\"expandedChapters.includes(index) || chapter.content.length <= 200\"\n                class=\"full-content\"\n              >\n                {{ chapter.content }}\n              </div>\n              <div v-else class=\"preview-content\">\n                {{ chapter.content.substring(0, 200) }}...\n              </div>\n              \n              <!-- 投递物品信息 -->\n              <div v-if=\"chapter.deliveredItem\" class=\"delivered-item\">\n                <el-divider content-position=\"left\">\n                  <i class=\"el-icon-box\"></i>\n                  投递物品\n                </el-divider>\n                <div class=\"item-info\">\n                  <p><strong>物品名称：</strong>{{ chapter.deliveredItem.name }}</p>\n                  <p><strong>物品描述：</strong>{{ chapter.deliveredItem.description }}</p>\n                  <p><strong>投递时间：</strong>{{ formatTime(chapter.deliveredItem.deliveryDate) }}</p>\n                </div>\n              </div>\n              \n              <!-- AI生成标识 -->\n              <div class=\"chapter-footer\">\n                <div class=\"generation-info\">\n                  <el-tag \n                    v-if=\"chapter.aiGenerated !== undefined\" \n                    :type=\"chapter.aiGenerated ? 'success' : 'warning'\"\n                    size=\"mini\"\n                  >\n                    {{ chapter.aiGenerated ? 'AI生成' : '备用内容' }}\n                  </el-tag>\n                </div>\n                <div class=\"chapter-day\">\n                  第 {{ chapter.day }} 天\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-timeline-item>\n      </el-timeline>\n      \n      <!-- 空状态 -->\n      <el-empty v-else description=\"还没有故事章节\" />\n    </div>\n\n    <!-- 操作按钮 -->\n    <div class=\"story-actions\">\n      <el-button type=\"primary\" @click=\"refreshStory\" :loading=\"loading\">\n        <i class=\"el-icon-refresh\"></i>\n        刷新故事\n      </el-button>\n      <el-button type=\"success\" @click=\"$router.push('/delivery')\">\n        <i class=\"el-icon-box\"></i>\n        投递物品\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Story',\n  data() {\n    return {\n      storyData: {},\n      loading: false,\n      expandedChapters: []\n    }\n  },\n  async created() {\n    await this.loadStory()\n  },\n  methods: {\n    async loadStory() {\n      this.loading = true\n      try {\n        const response = await this.$http.get('/story')\n        if (response.data.success) {\n          this.storyData = response.data.data\n          // 默认展开最新的章节\n          if (this.storyData.chapters && this.storyData.chapters.length > 0) {\n            this.expandedChapters = [this.storyData.chapters.length - 1]\n          }\n        }\n      } catch (error) {\n        console.error('Failed to load story:', error)\n        this.$message.error('加载故事失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async refreshStory() {\n      await this.loadStory()\n      this.$message.success('故事已刷新')\n    },\n    \n    toggleChapterExpand(index) {\n      const expandedIndex = this.expandedChapters.indexOf(index)\n      if (expandedIndex > -1) {\n        this.expandedChapters.splice(expandedIndex, 1)\n      } else {\n        this.expandedChapters.push(index)\n      }\n    },\n    \n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleString('zh-CN')\n    },\n    \n    getTimelineType(chapter) {\n      if (chapter.day === 0) return 'info'\n      if (chapter.deliveredItem) return 'primary'\n      return 'success'\n    },\n    \n    getTimelineIcon(chapter) {\n      if (chapter.day === 0) return 'el-icon-star-on'\n      if (chapter.deliveredItem) return 'el-icon-box'\n      return 'el-icon-edit'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.story {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.title-card {\n  margin-bottom: 20px;\n}\n\n.story-header {\n  text-align: center;\n}\n\n.story-title {\n  color: #303133;\n  margin-bottom: 15px;\n  font-size: 28px;\n}\n\n.story-meta {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n}\n\n.chapters-container {\n  margin-bottom: 20px;\n}\n\n.chapter-card {\n  margin-bottom: 0;\n}\n\n.chapter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-title {\n  font-weight: bold;\n  color: #303133;\n}\n\n.chapter-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.chapter-content {\n  line-height: 1.8;\n  color: #606266;\n}\n\n.full-content,\n.preview-content {\n  white-space: pre-wrap;\n  margin-bottom: 15px;\n}\n\n.delivered-item {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin: 15px 0;\n}\n\n.item-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.chapter-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #ebeef5;\n}\n\n.chapter-day {\n  font-size: 12px;\n  color: #909399;\n}\n\n.story-actions {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.story-actions .el-button {\n  margin: 0 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .story {\n    padding: 0 10px;\n  }\n  \n  .story-title {\n    font-size: 24px;\n  }\n  \n  .chapter-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n  \n  .chapter-actions {\n    width: 100%;\n    justify-content: space-between;\n  }\n  \n  .story-actions .el-button {\n    display: block;\n    width: 100%;\n    margin: 10px 0;\n  }\n}\n</style>\n"]}]}