{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue?vue&type=style&index=0&id=5031b956&scoped=true&lang=css", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue", "mtime": 1756200035137}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756201380880}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756201382571}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756201381511}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Stats.vue"], "names": [], "mappings": ";AAqUA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Stats.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"stats\">\n    <!-- 游戏统计概览 -->\n    <el-card class=\"overview-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>游戏统计概览</span>\n        <el-button type=\"text\" @click=\"loadData\" :loading=\"loading\">\n          <i class=\"el-icon-refresh\"></i>\n          刷新\n        </el-button>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ gameStats.currentDay || 0 }}</div>\n            <div class=\"stat-label\">当前天数</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ gameStats.totalDeliveries || 0 }}</div>\n            <div class=\"stat-label\">总投递次数</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ storyStats.totalChapters || 0 }}</div>\n            <div class=\"stat-label\">故事章节</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ storyStats.totalWords || 0 }}</div>\n            <div class=\"stat-label\">总字数</div>\n          </div>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 游戏进度 -->\n    <el-card class=\"progress-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>游戏进度</span>\n      </div>\n      \n      <div class=\"progress-content\">\n        <div class=\"progress-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">游戏开始时间：</span>\n            <span class=\"info-value\">{{ formatTime(gameStats.gameStartDate) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">游戏持续时间：</span>\n            <span class=\"info-value\">{{ gameStats.gameDuration || 0 }} 天</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">平均每章字数：</span>\n            <span class=\"info-value\">{{ gameStats.averageWordsPerChapter || 0 }} 字</span>\n          </div>\n        </div>\n        \n        <div class=\"progress-visual\">\n          <el-progress \n            :percentage=\"Math.min((gameStats.currentDay || 0) * 10, 100)\" \n            :stroke-width=\"20\"\n            :text-inside=\"true\"\n            status=\"success\"\n          />\n          <p class=\"progress-text\">荒岛生存进度</p>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 投递物品统计 -->\n    <el-card class=\"items-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>投递物品统计</span>\n        <el-tag type=\"info\">共 {{ deliveredItems.length }} 件物品</el-tag>\n      </div>\n      \n      <div v-if=\"deliveredItems.length > 0\">\n        <div class=\"items-grid\">\n          <div \n            v-for=\"(item, index) in deliveredItems\" \n            :key=\"index\"\n            class=\"item-card\"\n          >\n            <div class=\"item-header\">\n              <h4 class=\"item-name\">{{ item.name }}</h4>\n              <el-tag size=\"mini\">第{{ item.day }}天</el-tag>\n            </div>\n            <p class=\"item-description\">{{ item.description }}</p>\n            <div class=\"item-footer\">\n              <span class=\"item-date\">{{ formatTime(item.deliveryDate) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <el-empty v-else description=\"还没有投递任何物品\" />\n    </el-card>\n\n    <!-- 故事统计 -->\n    <el-card class=\"story-stats-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>故事统计</span>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :xs=\"24\" :sm=\"12\">\n          <div class=\"chart-container\">\n            <h4>章节字数分布</h4>\n            <div class=\"simple-chart\">\n              <div \n                v-for=\"(chapter, index) in recentChapters\" \n                :key=\"index\"\n                class=\"chart-bar\"\n              >\n                <div class=\"bar-label\">第{{ chapter.day }}天</div>\n                <div class=\"bar-container\">\n                  <div \n                    class=\"bar-fill\" \n                    :style=\"{ width: getBarWidth(chapter.content.length) + '%' }\"\n                  ></div>\n                </div>\n                <div class=\"bar-value\">{{ chapter.content.length }}字</div>\n              </div>\n            </div>\n          </div>\n        </el-col>\n        \n        <el-col :xs=\"24\" :sm=\"12\">\n          <div class=\"achievements\">\n            <h4>成就系统</h4>\n            <div class=\"achievement-list\">\n              <div \n                v-for=\"achievement in achievements\" \n                :key=\"achievement.id\"\n                class=\"achievement-item\"\n                :class=\"{ 'unlocked': achievement.unlocked }\"\n              >\n                <i :class=\"achievement.icon\"></i>\n                <div class=\"achievement-info\">\n                  <div class=\"achievement-name\">{{ achievement.name }}</div>\n                  <div class=\"achievement-desc\">{{ achievement.description }}</div>\n                </div>\n                <div class=\"achievement-status\">\n                  <i v-if=\"achievement.unlocked\" class=\"el-icon-check\"></i>\n                  <i v-else class=\"el-icon-lock\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 操作按钮 -->\n    <div class=\"actions\">\n      <el-button type=\"danger\" @click=\"showResetDialog = true\">\n        <i class=\"el-icon-refresh-left\"></i>\n        重置游戏\n      </el-button>\n    </div>\n\n    <!-- 重置确认对话框 -->\n    <el-dialog\n      title=\"重置游戏\"\n      :visible.sync=\"showResetDialog\"\n      width=\"400px\"\n      center\n    >\n      <div class=\"reset-dialog-content\">\n        <i class=\"el-icon-warning\" style=\"color: #f56c6c; font-size: 48px;\"></i>\n        <p>确定要重置游戏吗？</p>\n        <p style=\"color: #909399; font-size: 14px;\">这将删除所有故事内容和投递记录，且无法恢复！</p>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showResetDialog = false\">取消</el-button>\n        <el-button type=\"danger\" @click=\"resetGame\" :loading=\"resetting\">\n          确定重置\n        </el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Stats',\n  data() {\n    return {\n      loading: false,\n      gameStats: {},\n      storyStats: {},\n      deliveredItems: [],\n      recentChapters: [],\n      showResetDialog: false,\n      resetting: false,\n      achievements: [\n        {\n          id: 1,\n          name: '初来乍到',\n          description: '投递第一个物品',\n          icon: 'el-icon-box',\n          unlocked: false\n        },\n        {\n          id: 2,\n          name: '生存专家',\n          description: '生存超过7天',\n          icon: 'el-icon-trophy',\n          unlocked: false\n        },\n        {\n          id: 3,\n          name: '物资丰富',\n          description: '投递超过10个物品',\n          icon: 'el-icon-present',\n          unlocked: false\n        },\n        {\n          id: 4,\n          name: '故事大师',\n          description: '故事总字数超过5000字',\n          icon: 'el-icon-edit',\n          unlocked: false\n        }\n      ]\n    }\n  },\n  async created() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      this.loading = true\n      try {\n        await Promise.all([\n          this.loadGameStats(),\n          this.loadStoryStats(),\n          this.loadStoryData()\n        ])\n        this.updateAchievements()\n      } catch (error) {\n        console.error('Failed to load stats:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadGameStats() {\n      try {\n        const response = await this.$http.get('/game/statistics')\n        if (response.data.success) {\n          this.gameStats = response.data.data\n          this.deliveredItems = response.data.data.itemsDelivered || []\n        }\n      } catch (error) {\n        console.error('Failed to load game stats:', error)\n      }\n    },\n    \n    async loadStoryStats() {\n      try {\n        const response = await this.$http.get('/story/stats')\n        if (response.data.success) {\n          this.storyStats = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load story stats:', error)\n      }\n    },\n    \n    async loadStoryData() {\n      try {\n        const response = await this.$http.get('/story')\n        if (response.data.success) {\n          this.recentChapters = response.data.data.chapters.slice(-5) || []\n        }\n      } catch (error) {\n        console.error('Failed to load story data:', error)\n      }\n    },\n    \n    async resetGame() {\n      this.resetting = true\n      try {\n        const response = await this.$http.post('/game/reset')\n        if (response.data.success) {\n          this.$message.success('游戏重置成功！')\n          this.showResetDialog = false\n          await this.loadData()\n          this.$router.push('/')\n        }\n      } catch (error) {\n        this.$message.error('重置失败，请重试')\n      } finally {\n        this.resetting = false\n      }\n    },\n    \n    updateAchievements() {\n      // 更新成就状态\n      this.achievements[0].unlocked = this.gameStats.totalDeliveries > 0\n      this.achievements[1].unlocked = this.gameStats.currentDay > 7\n      this.achievements[2].unlocked = this.gameStats.totalDeliveries > 10\n      this.achievements[3].unlocked = this.storyStats.totalWords > 5000\n    },\n    \n    getBarWidth(wordCount) {\n      const maxWords = Math.max(...this.recentChapters.map(ch => ch.content.length))\n      return maxWords > 0 ? (wordCount / maxWords) * 100 : 0\n    },\n    \n    formatTime(timestamp) {\n      if (!timestamp) return '未知'\n      return new Date(timestamp).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.stats {\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.overview-card,\n.progress-card,\n.items-card,\n.story-stats-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.stat-value {\n  font-size: 32px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.progress-content {\n  display: flex;\n  gap: 30px;\n  align-items: center;\n}\n\n.progress-info {\n  flex: 1;\n}\n\n.info-item {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.info-label {\n  color: #909399;\n  min-width: 120px;\n}\n\n.info-value {\n  color: #303133;\n  font-weight: 500;\n}\n\n.progress-visual {\n  flex: 1;\n  text-align: center;\n}\n\n.progress-text {\n  margin-top: 10px;\n  color: #606266;\n}\n\n.items-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 15px;\n}\n\n.item-card {\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fafafa;\n}\n\n.item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.item-name {\n  color: #303133;\n  margin: 0;\n}\n\n.item-description {\n  color: #606266;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.item-footer {\n  text-align: right;\n}\n\n.item-date {\n  color: #909399;\n  font-size: 12px;\n}\n\n.chart-container h4,\n.achievements h4 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.simple-chart {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.chart-bar {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.bar-label {\n  min-width: 60px;\n  font-size: 12px;\n  color: #606266;\n}\n\n.bar-container {\n  flex: 1;\n  height: 20px;\n  background-color: #f5f7fa;\n  border-radius: 10px;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #409eff, #67c23a);\n  transition: width 0.3s ease;\n}\n\n.bar-value {\n  min-width: 50px;\n  font-size: 12px;\n  color: #909399;\n  text-align: right;\n}\n\n.achievement-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.achievement-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px;\n  border-radius: 4px;\n  background-color: #f5f7fa;\n  opacity: 0.6;\n}\n\n.achievement-item.unlocked {\n  background-color: #f0f9ff;\n  opacity: 1;\n}\n\n.achievement-item i:first-child {\n  font-size: 24px;\n  color: #909399;\n}\n\n.achievement-item.unlocked i:first-child {\n  color: #409eff;\n}\n\n.achievement-info {\n  flex: 1;\n}\n\n.achievement-name {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.achievement-desc {\n  font-size: 12px;\n  color: #606266;\n}\n\n.achievement-status i {\n  font-size: 18px;\n  color: #909399;\n}\n\n.achievement-item.unlocked .achievement-status i {\n  color: #67c23a;\n}\n\n.actions {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.reset-dialog-content {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.reset-dialog-content p {\n  margin: 15px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .stats {\n    padding: 0 10px;\n  }\n  \n  .progress-content {\n    flex-direction: column;\n    gap: 20px;\n  }\n  \n  .items-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .chart-bar {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n  \n  .bar-container {\n    width: 100%;\n  }\n}\n</style>\n"]}]}