<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 头部 -->
      <el-header class="app-header">
        <div class="header-content">
          <h1 class="app-title">
            <i class="el-icon-reading"></i>
            AI小说生成器 - 荒岛求生记
          </h1>
          <div class="game-status">
            <el-tag v-if="gameStatus" :type="gameStatus.canDeliverToday ? 'success' : 'info'">
              第 {{ gameStatus.currentDay }} 天
            </el-tag>
            <el-tag v-if="gameStatus" :type="gameStatus.canDeliverToday ? 'primary' : 'warning'" style="margin-left: 10px;">
              {{ gameStatus.canDeliverToday ? '可以投递' : '今日已投递' }}
            </el-tag>
          </div>
        </div>
      </el-header>

      <!-- 主体内容 -->
      <el-main class="app-main">
        <router-view @game-status-updated="updateGameStatus" />
      </el-main>

      <!-- 底部导航 -->
      <el-footer class="app-footer">
        <el-menu
          :default-active="$route.path"
          mode="horizontal"
          router
          class="footer-menu"
        >
          <el-menu-item index="/">
            <i class="el-icon-house"></i>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="/story">
            <i class="el-icon-notebook-1"></i>
            <span>故事</span>
          </el-menu-item>
          <el-menu-item index="/delivery">
            <i class="el-icon-box"></i>
            <span>投递</span>
          </el-menu-item>
          <el-menu-item index="/stats">
            <i class="el-icon-data-analysis"></i>
            <span>统计</span>
          </el-menu-item>
        </el-menu>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      gameStatus: null
    }
  },
  async created() {
    await this.loadGameStatus()
  },
  methods: {
    async loadGameStatus() {
      try {
        const response = await this.$http.get('/game/status')
        if (response.data.success) {
          this.gameStatus = response.data.data
        }
      } catch (error) {
        console.error('Failed to load game status:', error)
      }
    },
    updateGameStatus(newStatus) {
      this.gameStatus = { ...this.gameStatus, ...newStatus }
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

.app-container {
  height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.app-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.app-title i {
  margin-right: 10px;
}

.game-status {
  display: flex;
  align-items: center;
}

.app-main {
  background-color: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

.app-footer {
  height: auto !important;
  padding: 0;
  border-top: 1px solid #e4e7ed;
}

.footer-menu {
  border: none;
}

.footer-menu .el-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 20px;
  min-width: 80px;
}

.footer-menu .el-menu-item i {
  font-size: 18px;
  margin-bottom: 4px;
}

.footer-menu .el-menu-item span {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .app-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .app-main {
    padding: 15px;
  }
  
  .footer-menu .el-menu-item {
    padding: 8px 15px;
    min-width: 60px;
  }
}
</style>
