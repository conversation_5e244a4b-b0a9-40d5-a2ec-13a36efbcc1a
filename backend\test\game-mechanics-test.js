const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

class GameMechanicsTest {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE,
      timeout: 30000
    });
  }

  async testDeliveryMechanics() {
    console.log('🎮 测试新的游戏机制...\n');

    try {
      // 1. 获取初始游戏状态
      console.log('1. 获取初始游戏状态...');
      const initialState = await this.client.get('/game/status');
      console.log('初始状态:', {
        currentDay: initialState.data.data.currentDay,
        canDeliverToday: initialState.data.data.canDeliverToday,
        totalDeliveries: initialState.data.data.totalDeliveries
      });

      // 2. 测试第一次投递
      console.log('\n2. 测试第一次投递...');
      const delivery1 = await this.client.post('/delivery', {
        item: '测试物品1',
        description: '这是第一个测试物品，用来验证游戏机制是否正常工作，应该生成详细的故事内容'
      });

      console.log('投递结果:', {
        success: delivery1.data.success,
        message: delivery1.data.message,
        newDay: delivery1.data.data.gameState.currentDay,
        canDeliverAgain: delivery1.data.data.gameState.canDeliverToday,
        storyLength: delivery1.data.data.chapter.content.length
      });

      console.log('生成的故事长度:', delivery1.data.data.chapter.content.length, '字符');
      console.log('故事预览:', delivery1.data.data.chapter.content.substring(0, 100) + '...');

      // 3. 立即测试第二次投递（验证不需要等待）
      console.log('\n3. 立即测试第二次投递...');
      const delivery2 = await this.client.post('/delivery', {
        item: '测试物品2',
        description: '这是第二个测试物品，用来验证投递后可以立即再次投递，不需要等待真实时间'
      });

      console.log('第二次投递结果:', {
        success: delivery2.data.success,
        newDay: delivery2.data.data.gameState.currentDay,
        canDeliverAgain: delivery2.data.data.gameState.canDeliverToday,
        storyLength: delivery2.data.data.chapter.content.length
      });

      // 4. 获取完整故事，验证连续性
      console.log('\n4. 获取完整故事...');
      const story = await this.client.get('/story');
      console.log('故事章节数:', story.data.data.chapters.length);
      console.log('当前游戏天数:', story.data.data.currentDay);

      // 5. 验证故事内容质量
      console.log('\n5. 验证故事内容质量...');
      const chapters = story.data.data.chapters;
      chapters.forEach((chapter, index) => {
        if (chapter.deliveredItem) {
          console.log(`第${chapter.day}天 - ${chapter.deliveredItem.name}:`);
          console.log(`  字数: ${chapter.content.length}`);
          console.log(`  AI生成: ${chapter.aiGenerated ? '是' : '否'}`);
          console.log(`  备用内容: ${chapter.fallback ? '是' : '否'}`);
        }
      });

      console.log('\n✅ 游戏机制测试完成！');
      console.log('新机制特点:');
      console.log('- 投递后立即进入下一天');
      console.log('- 不需要等待真实时间');
      console.log('- 生成详细的故事内容');
      console.log('- 可以连续投递物品');

      return true;

    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      if (error.response) {
        console.error('错误详情:', error.response.data);
      }
      return false;
    }
  }

  async testStoryQuality() {
    console.log('\n📖 测试故事质量...');

    try {
      // 投递一个物品并检查生成的故事质量
      const delivery = await this.client.post('/delivery', {
        item: '瑞士军刀',
        description: '一把多功能的瑞士军刀，包含刀片、螺丝刀、开瓶器等多种工具，非常实用'
      });

      const chapter = delivery.data.data.chapter;
      const content = chapter.content;

      console.log('故事质量分析:');
      console.log('- 字数:', content.length);
      console.log('- 是否包含物品名称:', content.includes('瑞士军刀') ? '是' : '否');
      console.log('- 是否描述了使用过程:', content.includes('使用') || content.includes('利用') ? '是' : '否');
      console.log('- 是否有环境描写:', content.includes('海滩') || content.includes('阳光') || content.includes('海风') ? '是' : '否');
      console.log('- 是否有心理描写:', content.includes('想') || content.includes('感') || content.includes('心') ? '是' : '否');

      if (content.length >= 300) {
        console.log('✅ 故事长度符合要求');
      } else {
        console.log('⚠️  故事长度可能不够详细');
      }

      return true;

    } catch (error) {
      console.error('❌ 故事质量测试失败:', error.message);
      return false;
    }
  }
}

async function main() {
  const tester = new GameMechanicsTest();

  console.log('🚀 开始测试新的游戏机制...\n');

  try {
    const mechanicsTest = await tester.testDeliveryMechanics();
    const qualityTest = await tester.testStoryQuality();

    if (mechanicsTest && qualityTest) {
      console.log('\n🎉 所有测试通过！新的游戏机制工作正常');
    } else {
      console.log('\n⚠️  部分测试失败，请检查配置');
    }

  } catch (error) {
    console.error('测试运行失败:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().then(() => {
    console.log('\n测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = GameMechanicsTest;
