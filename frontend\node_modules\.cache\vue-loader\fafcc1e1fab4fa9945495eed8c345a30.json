{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue?vue&type=template&id=fae5bece&scoped=true", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue", "mtime": 1756199881921}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}