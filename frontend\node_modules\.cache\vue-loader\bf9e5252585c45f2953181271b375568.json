{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue?vue&type=template&id=5031b956&scoped=true", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue", "mtime": 1756200035137}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}