@echo off
echo 修复Vue配置问题...
echo ====================

cd frontend

echo 当前vue.config.js内容:
type vue.config.js

echo.
echo 选择修复方案:
echo 1. 使用简化配置（推荐）
echo 2. 使用最小配置
echo 3. 删除配置文件（使用默认配置）

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 使用简化配置...
    echo module.exports = { > vue.config.js
    echo   devServer: { >> vue.config.js
    echo     port: 8080, >> vue.config.js
    echo     proxy: { >> vue.config.js
    echo       '/api': { >> vue.config.js
    echo         target: 'http://localhost:3000', >> vue.config.js
    echo         changeOrigin: true >> vue.config.js
    echo       } >> vue.config.js
    echo     } >> vue.config.js
    echo   } >> vue.config.js
    echo } >> vue.config.js
    echo 简化配置已应用
)

if "%choice%"=="2" (
    echo 使用最小配置...
    copy vue.config.minimal.js vue.config.js
    echo 最小配置已应用
)

if "%choice%"=="3" (
    echo 删除配置文件...
    del vue.config.js
    echo 配置文件已删除，将使用默认配置
)

echo.
echo 配置修复完成！
echo 现在尝试启动: npm run serve

cd ..
pause
