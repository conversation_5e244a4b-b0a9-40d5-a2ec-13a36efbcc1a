import Vue from 'vue'
import App from './App.vue'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'

// 配置Element UI
Vue.use(ElementUI)

// 配置axios
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000/api'
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    // 可以在这里添加loading状态
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 统一错误处理
    if (error.response) {
      const message = error.response.data?.error || '请求失败'
      ElementUI.Message.error(message)
    } else if (error.request) {
      ElementUI.Message.error('网络连接失败，请检查网络')
    } else {
      ElementUI.Message.error('请求配置错误')
    }
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

Vue.config.productionTip = false

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
