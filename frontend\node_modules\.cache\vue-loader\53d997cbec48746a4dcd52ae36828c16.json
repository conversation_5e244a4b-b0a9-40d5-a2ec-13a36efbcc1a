{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&scoped=true&lang=css", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Home.vue", "mtime": 1756199881921}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756201380880}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756201382571}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756201381511}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAqMA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"home\">\n    <!-- 欢迎卡片 -->\n    <el-card class=\"welcome-card\" shadow=\"hover\">\n      <div class=\"welcome-content\">\n        <div class=\"welcome-text\">\n          <h2>欢迎来到荒岛求生记</h2>\n          <p>在这个神奇的世界里，你可以通过投递物品来影响主角李明的荒岛生活。每一个物品都会带来新的故事章节，让我们一起见证这个奇妙的冒险吧！</p>\n        </div>\n        <div class=\"welcome-image\">\n          <i class=\"el-icon-sunny\" style=\"font-size: 80px; color: #f39c12;\"></i>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 游戏状态 -->\n    <el-row :gutter=\"20\" class=\"status-row\">\n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-calendar status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">第 {{ gameStatus.currentDay || 1 }} 天</div>\n              <div class=\"status-label\">当前天数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-box status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ gameStatus.totalDeliveries || 0 }}</div>\n              <div class=\"status-label\">总投递次数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-check status-icon\" :class=\"{ 'can-deliver': gameStatus.canDeliverToday }\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ gameStatus.canDeliverToday ? '可投递' : '已投递' }}</div>\n              <div class=\"status-label\">今日状态</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      \n      <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n        <el-card class=\"status-card\">\n          <div class=\"status-item\">\n            <i class=\"el-icon-notebook-1 status-icon\"></i>\n            <div class=\"status-info\">\n              <div class=\"status-value\">{{ storyStats.totalChapters || 0 }}</div>\n              <div class=\"status-label\">故事章节</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 最新章节预览 -->\n    <el-card class=\"latest-chapter-card\" shadow=\"hover\" v-if=\"latestChapter\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>最新章节</span>\n        <el-button type=\"text\" @click=\"$router.push('/story')\">查看全部</el-button>\n      </div>\n      <div class=\"chapter-preview\">\n        <h3 class=\"chapter-title\">{{ latestChapter.title }}</h3>\n        <p class=\"chapter-content\">{{ latestChapter.content.substring(0, 200) }}...</p>\n        <div class=\"chapter-meta\">\n          <el-tag size=\"mini\" v-if=\"latestChapter.deliveredItem\">\n            物品：{{ latestChapter.deliveredItem.name }}\n          </el-tag>\n          <span class=\"chapter-time\">{{ formatTime(latestChapter.timestamp) }}</span>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 快速操作 -->\n    <el-card class=\"quick-actions-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>快速操作</span>\n      </div>\n      <div class=\"quick-actions\">\n        <el-button \n          type=\"primary\" \n          size=\"large\" \n          :disabled=\"!gameStatus.canDeliverToday\"\n          @click=\"$router.push('/delivery')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-box\"></i>\n          {{ gameStatus.canDeliverToday ? '投递物品' : '今日已投递' }}\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          size=\"large\" \n          @click=\"$router.push('/story')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-reading\"></i>\n          阅读故事\n        </el-button>\n        \n        <el-button \n          type=\"info\" \n          size=\"large\" \n          @click=\"$router.push('/stats')\"\n          class=\"action-button\"\n        >\n          <i class=\"el-icon-data-analysis\"></i>\n          查看统计\n        </el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      gameStatus: {},\n      storyStats: {},\n      latestChapter: null,\n      loading: false\n    }\n  },\n  async created() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      this.loading = true\n      try {\n        await Promise.all([\n          this.loadGameStatus(),\n          this.loadStoryStats(),\n          this.loadLatestChapter()\n        ])\n      } catch (error) {\n        console.error('Failed to load data:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadGameStatus() {\n      try {\n        const response = await this.$http.get('/game/status')\n        if (response.data.success) {\n          this.gameStatus = response.data.data\n          this.$emit('game-status-updated', this.gameStatus)\n        }\n      } catch (error) {\n        console.error('Failed to load game status:', error)\n      }\n    },\n    \n    async loadStoryStats() {\n      try {\n        const response = await this.$http.get('/story/stats')\n        if (response.data.success) {\n          this.storyStats = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load story stats:', error)\n      }\n    },\n    \n    async loadLatestChapter() {\n      try {\n        const response = await this.$http.get('/story/latest')\n        if (response.data.success) {\n          this.latestChapter = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load latest chapter:', error)\n      }\n    },\n    \n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.home {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.welcome-card {\n  margin-bottom: 20px;\n}\n\n.welcome-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.welcome-text {\n  flex: 1;\n}\n\n.welcome-text h2 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.welcome-text p {\n  color: #606266;\n  line-height: 1.6;\n}\n\n.welcome-image {\n  margin-left: 20px;\n}\n\n.status-row {\n  margin-bottom: 20px;\n}\n\n.status-card {\n  margin-bottom: 20px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n}\n\n.status-icon {\n  font-size: 32px;\n  color: #909399;\n  margin-right: 15px;\n}\n\n.status-icon.can-deliver {\n  color: #67c23a;\n}\n\n.status-info {\n  flex: 1;\n}\n\n.status-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.status-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.latest-chapter-card,\n.quick-actions-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-preview {\n  padding: 10px 0;\n}\n\n.chapter-title {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.chapter-content {\n  color: #606266;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.chapter-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.chapter-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n.quick-actions {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.action-button {\n  flex: 1;\n  min-width: 120px;\n}\n\n.action-button i {\n  margin-right: 5px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .welcome-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .welcome-image {\n    margin-left: 0;\n    margin-top: 20px;\n  }\n  \n  .quick-actions {\n    flex-direction: column;\n  }\n  \n  .action-button {\n    width: 100%;\n  }\n}\n</style>\n"]}]}