const express = require('express');
const router = express.Router();
const moment = require('moment');
const dataService = require('../services/dataService');
const gameService = require('../services/gameService');

// 获取游戏状态
router.get('/status', async (req, res) => {
  try {
    const gameState = await dataService.getGameState();
    
    // 检查是否可以投递
    const canDeliver = await checkCanDeliverToday(gameState);
    
    // 更新游戏状态
    gameState.canDeliverToday = canDeliver;
    
    res.json({
      success: true,
      data: {
        currentDay: gameState.currentDay,
        canDeliverToday: canDeliver,
        totalDeliveries: gameState.totalDeliveries,
        gameStartDate: gameState.gameStartDate,
        lastDeliveryDate: gameState.lastDeliveryDate,
        playerStats: gameState.playerStats
      }
    });
  } catch (error) {
    console.error('Error fetching game status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch game status'
    });
  }
});

// 重置游戏
router.post('/reset', async (req, res) => {
  try {
    // 重置游戏状态
    const newGameState = {
      currentDay: 1,
      lastDeliveryDate: null,
      canDeliverToday: true,
      totalDeliveries: 0,
      gameStartDate: new Date().toISOString(),
      playerStats: {
        itemsDelivered: [],
        storyProgress: 1
      }
    };

    // 重置故事数据
    const newStoryData = {
      title: "荒岛求生记",
      chapters: [
        {
          day: 0,
          title: "序章：意外降临",
          content: "李明原本是一名普通的上班族，在一次商务旅行中遭遇了飞机失事。当他从昏迷中醒来时，发现自己躺在一个陌生的海滩上。四周是茂密的热带植被，远处传来海浪拍打礁石的声音。\n\n他挣扎着站起身来，环顾四周。这是一个不大的岛屿，看起来完全与世隔绝。李明心中涌起一阵恐慌，但很快强迫自己冷静下来。他知道，在这种情况下，保持理智是生存的关键。\n\n就在他准备寻找淡水和食物时，海滩上一个奇怪的木箱引起了他的注意。箱子看起来很新，上面还贴着一张纸条：'给荒岛上的朋友：这是你的第一份礼物，希望对你有帮助。——神秘的朋友'。\n\n李明困惑地打开箱子，里面装着一些基本的生存用品：打火机、小刀、绳子和一些压缩饼干。这些东西来得正是时候，但更让他疑惑的是，这个神秘的朋友是谁？为什么会知道他在这里？\n\n夜幕降临，李明在海滩上生起了篝火。望着满天繁星，他心中既有对未来的不安，也有一丝莫名的期待。也许，明天还会有新的惊喜等着他。",
          deliveredItem: null,
          timestamp: new Date().toISOString()
        }
      ],
      currentDay: 1,
      lastUpdated: new Date().toISOString()
    };

    await dataService.saveGameState(newGameState);
    await dataService.saveStoryData(newStoryData);

    res.json({
      success: true,
      message: 'Game reset successfully',
      data: newGameState
    });
  } catch (error) {
    console.error('Error resetting game:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset game'
    });
  }
});

// 获取游戏统计
router.get('/statistics', async (req, res) => {
  try {
    const stats = await gameService.getGameStatistics();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching game statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch game statistics'
    });
  }
});

// 获取玩家成就
router.get('/achievements', async (req, res) => {
  try {
    const achievements = await gameService.getPlayerAchievements();

    res.json({
      success: true,
      data: achievements
    });
  } catch (error) {
    console.error('Error fetching achievements:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch achievements'
    });
  }
});

// 检查今天是否可以投递
async function checkCanDeliverToday(gameState) {
  if (!gameState.lastDeliveryDate) {
    return true;
  }

  const lastDelivery = moment(gameState.lastDeliveryDate);
  const today = moment();
  
  // 如果上次投递不是今天，则可以投递
  return !lastDelivery.isSame(today, 'day');
}

module.exports = router;
