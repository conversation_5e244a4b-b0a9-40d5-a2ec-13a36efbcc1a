@echo off
echo 测试前端启动...
echo ==================

cd frontend

echo 检查package.json...
if not exist package.json (
    echo 错误: package.json不存在
    pause
    exit /b 1
)

echo 检查vue.config.js...
if not exist vue.config.js (
    echo 错误: vue.config.js不存在
    pause
    exit /b 1
)

echo 检查node_modules...
if not exist node_modules (
    echo node_modules不存在，正在安装依赖...
    npm install --legacy-peer-deps
)

echo 尝试启动前端服务...
npm run serve

cd ..
pause
