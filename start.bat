@echo off
echo ========================================
echo    AI小说生成器 - 荒岛求生记
echo ========================================
echo.

echo 正在检查环境...

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js 已安装: 
node --version

echo.
echo 正在启动后端服务...

:: 检查后端依赖
if not exist "backend\node_modules" (
    echo 正在安装后端依赖...
    cd backend
    npm install
    cd ..
)

:: 检查环境变量文件
if not exist "backend\.env" (
    echo 正在创建环境变量文件...
    copy "backend\.env.example" "backend\.env"
    echo.
    echo 重要提示: 请编辑 backend\.env 文件，设置您的 DeepSeek API Key
    echo 文件位置: backend\.env
    echo 设置项: DEEPSEEK_API_KEY=your_api_key_here
    echo.
    echo 如果没有API Key，系统将使用备用内容生成功能
    echo.
)

:: 启动后端服务
start "AI小说生成器-后端" cmd /k "cd backend && npm start"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo 正在启动前端服务...

:: 检查前端依赖
if not exist "frontend\node_modules" (
    echo 正在安装前端依赖...
    cd frontend
    npm install
    cd ..
)

:: 启动前端服务
start "AI小说生成器-前端" cmd /k "cd frontend && npm run serve"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 后端服务: http://localhost:3000
echo 前端服务: http://localhost:8080
echo.
echo 请等待几秒钟让服务完全启动，然后访问:
echo http://localhost:8080
echo.
echo 按任意键退出...
pause >nul
