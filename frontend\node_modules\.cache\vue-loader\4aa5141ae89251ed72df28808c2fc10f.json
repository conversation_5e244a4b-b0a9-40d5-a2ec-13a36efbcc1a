{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Stats.vue", "mtime": 1756200035137}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Stats.vue"], "names": [], "mappings": ";AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Stats.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"stats\">\n    <!-- 游戏统计概览 -->\n    <el-card class=\"overview-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>游戏统计概览</span>\n        <el-button type=\"text\" @click=\"loadData\" :loading=\"loading\">\n          <i class=\"el-icon-refresh\"></i>\n          刷新\n        </el-button>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ gameStats.currentDay || 0 }}</div>\n            <div class=\"stat-label\">当前天数</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ gameStats.totalDeliveries || 0 }}</div>\n            <div class=\"stat-label\">总投递次数</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ storyStats.totalChapters || 0 }}</div>\n            <div class=\"stat-label\">故事章节</div>\n          </div>\n        </el-col>\n        <el-col :xs=\"12\" :sm=\"6\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ storyStats.totalWords || 0 }}</div>\n            <div class=\"stat-label\">总字数</div>\n          </div>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 游戏进度 -->\n    <el-card class=\"progress-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>游戏进度</span>\n      </div>\n      \n      <div class=\"progress-content\">\n        <div class=\"progress-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">游戏开始时间：</span>\n            <span class=\"info-value\">{{ formatTime(gameStats.gameStartDate) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">游戏持续时间：</span>\n            <span class=\"info-value\">{{ gameStats.gameDuration || 0 }} 天</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">平均每章字数：</span>\n            <span class=\"info-value\">{{ gameStats.averageWordsPerChapter || 0 }} 字</span>\n          </div>\n        </div>\n        \n        <div class=\"progress-visual\">\n          <el-progress \n            :percentage=\"Math.min((gameStats.currentDay || 0) * 10, 100)\" \n            :stroke-width=\"20\"\n            :text-inside=\"true\"\n            status=\"success\"\n          />\n          <p class=\"progress-text\">荒岛生存进度</p>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 投递物品统计 -->\n    <el-card class=\"items-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>投递物品统计</span>\n        <el-tag type=\"info\">共 {{ deliveredItems.length }} 件物品</el-tag>\n      </div>\n      \n      <div v-if=\"deliveredItems.length > 0\">\n        <div class=\"items-grid\">\n          <div \n            v-for=\"(item, index) in deliveredItems\" \n            :key=\"index\"\n            class=\"item-card\"\n          >\n            <div class=\"item-header\">\n              <h4 class=\"item-name\">{{ item.name }}</h4>\n              <el-tag size=\"mini\">第{{ item.day }}天</el-tag>\n            </div>\n            <p class=\"item-description\">{{ item.description }}</p>\n            <div class=\"item-footer\">\n              <span class=\"item-date\">{{ formatTime(item.deliveryDate) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <el-empty v-else description=\"还没有投递任何物品\" />\n    </el-card>\n\n    <!-- 故事统计 -->\n    <el-card class=\"story-stats-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>故事统计</span>\n      </div>\n      \n      <el-row :gutter=\"20\">\n        <el-col :xs=\"24\" :sm=\"12\">\n          <div class=\"chart-container\">\n            <h4>章节字数分布</h4>\n            <div class=\"simple-chart\">\n              <div \n                v-for=\"(chapter, index) in recentChapters\" \n                :key=\"index\"\n                class=\"chart-bar\"\n              >\n                <div class=\"bar-label\">第{{ chapter.day }}天</div>\n                <div class=\"bar-container\">\n                  <div \n                    class=\"bar-fill\" \n                    :style=\"{ width: getBarWidth(chapter.content.length) + '%' }\"\n                  ></div>\n                </div>\n                <div class=\"bar-value\">{{ chapter.content.length }}字</div>\n              </div>\n            </div>\n          </div>\n        </el-col>\n        \n        <el-col :xs=\"24\" :sm=\"12\">\n          <div class=\"achievements\">\n            <h4>成就系统</h4>\n            <div class=\"achievement-list\">\n              <div \n                v-for=\"achievement in achievements\" \n                :key=\"achievement.id\"\n                class=\"achievement-item\"\n                :class=\"{ 'unlocked': achievement.unlocked }\"\n              >\n                <i :class=\"achievement.icon\"></i>\n                <div class=\"achievement-info\">\n                  <div class=\"achievement-name\">{{ achievement.name }}</div>\n                  <div class=\"achievement-desc\">{{ achievement.description }}</div>\n                </div>\n                <div class=\"achievement-status\">\n                  <i v-if=\"achievement.unlocked\" class=\"el-icon-check\"></i>\n                  <i v-else class=\"el-icon-lock\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 操作按钮 -->\n    <div class=\"actions\">\n      <el-button type=\"danger\" @click=\"showResetDialog = true\">\n        <i class=\"el-icon-refresh-left\"></i>\n        重置游戏\n      </el-button>\n    </div>\n\n    <!-- 重置确认对话框 -->\n    <el-dialog\n      title=\"重置游戏\"\n      :visible.sync=\"showResetDialog\"\n      width=\"400px\"\n      center\n    >\n      <div class=\"reset-dialog-content\">\n        <i class=\"el-icon-warning\" style=\"color: #f56c6c; font-size: 48px;\"></i>\n        <p>确定要重置游戏吗？</p>\n        <p style=\"color: #909399; font-size: 14px;\">这将删除所有故事内容和投递记录，且无法恢复！</p>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showResetDialog = false\">取消</el-button>\n        <el-button type=\"danger\" @click=\"resetGame\" :loading=\"resetting\">\n          确定重置\n        </el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Stats',\n  data() {\n    return {\n      loading: false,\n      gameStats: {},\n      storyStats: {},\n      deliveredItems: [],\n      recentChapters: [],\n      showResetDialog: false,\n      resetting: false,\n      achievements: [\n        {\n          id: 1,\n          name: '初来乍到',\n          description: '投递第一个物品',\n          icon: 'el-icon-box',\n          unlocked: false\n        },\n        {\n          id: 2,\n          name: '生存专家',\n          description: '生存超过7天',\n          icon: 'el-icon-trophy',\n          unlocked: false\n        },\n        {\n          id: 3,\n          name: '物资丰富',\n          description: '投递超过10个物品',\n          icon: 'el-icon-present',\n          unlocked: false\n        },\n        {\n          id: 4,\n          name: '故事大师',\n          description: '故事总字数超过5000字',\n          icon: 'el-icon-edit',\n          unlocked: false\n        }\n      ]\n    }\n  },\n  async created() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      this.loading = true\n      try {\n        await Promise.all([\n          this.loadGameStats(),\n          this.loadStoryStats(),\n          this.loadStoryData()\n        ])\n        this.updateAchievements()\n      } catch (error) {\n        console.error('Failed to load stats:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadGameStats() {\n      try {\n        const response = await this.$http.get('/game/statistics')\n        if (response.data.success) {\n          this.gameStats = response.data.data\n          this.deliveredItems = response.data.data.itemsDelivered || []\n        }\n      } catch (error) {\n        console.error('Failed to load game stats:', error)\n      }\n    },\n    \n    async loadStoryStats() {\n      try {\n        const response = await this.$http.get('/story/stats')\n        if (response.data.success) {\n          this.storyStats = response.data.data\n        }\n      } catch (error) {\n        console.error('Failed to load story stats:', error)\n      }\n    },\n    \n    async loadStoryData() {\n      try {\n        const response = await this.$http.get('/story')\n        if (response.data.success) {\n          this.recentChapters = response.data.data.chapters.slice(-5) || []\n        }\n      } catch (error) {\n        console.error('Failed to load story data:', error)\n      }\n    },\n    \n    async resetGame() {\n      this.resetting = true\n      try {\n        const response = await this.$http.post('/game/reset')\n        if (response.data.success) {\n          this.$message.success('游戏重置成功！')\n          this.showResetDialog = false\n          await this.loadData()\n          this.$router.push('/')\n        }\n      } catch (error) {\n        this.$message.error('重置失败，请重试')\n      } finally {\n        this.resetting = false\n      }\n    },\n    \n    updateAchievements() {\n      // 更新成就状态\n      this.achievements[0].unlocked = this.gameStats.totalDeliveries > 0\n      this.achievements[1].unlocked = this.gameStats.currentDay > 7\n      this.achievements[2].unlocked = this.gameStats.totalDeliveries > 10\n      this.achievements[3].unlocked = this.storyStats.totalWords > 5000\n    },\n    \n    getBarWidth(wordCount) {\n      const maxWords = Math.max(...this.recentChapters.map(ch => ch.content.length))\n      return maxWords > 0 ? (wordCount / maxWords) * 100 : 0\n    },\n    \n    formatTime(timestamp) {\n      if (!timestamp) return '未知'\n      return new Date(timestamp).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.stats {\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.overview-card,\n.progress-card,\n.items-card,\n.story-stats-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.stat-value {\n  font-size: 32px;\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.progress-content {\n  display: flex;\n  gap: 30px;\n  align-items: center;\n}\n\n.progress-info {\n  flex: 1;\n}\n\n.info-item {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.info-label {\n  color: #909399;\n  min-width: 120px;\n}\n\n.info-value {\n  color: #303133;\n  font-weight: 500;\n}\n\n.progress-visual {\n  flex: 1;\n  text-align: center;\n}\n\n.progress-text {\n  margin-top: 10px;\n  color: #606266;\n}\n\n.items-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 15px;\n}\n\n.item-card {\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fafafa;\n}\n\n.item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.item-name {\n  color: #303133;\n  margin: 0;\n}\n\n.item-description {\n  color: #606266;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.item-footer {\n  text-align: right;\n}\n\n.item-date {\n  color: #909399;\n  font-size: 12px;\n}\n\n.chart-container h4,\n.achievements h4 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.simple-chart {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.chart-bar {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.bar-label {\n  min-width: 60px;\n  font-size: 12px;\n  color: #606266;\n}\n\n.bar-container {\n  flex: 1;\n  height: 20px;\n  background-color: #f5f7fa;\n  border-radius: 10px;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #409eff, #67c23a);\n  transition: width 0.3s ease;\n}\n\n.bar-value {\n  min-width: 50px;\n  font-size: 12px;\n  color: #909399;\n  text-align: right;\n}\n\n.achievement-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.achievement-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px;\n  border-radius: 4px;\n  background-color: #f5f7fa;\n  opacity: 0.6;\n}\n\n.achievement-item.unlocked {\n  background-color: #f0f9ff;\n  opacity: 1;\n}\n\n.achievement-item i:first-child {\n  font-size: 24px;\n  color: #909399;\n}\n\n.achievement-item.unlocked i:first-child {\n  color: #409eff;\n}\n\n.achievement-info {\n  flex: 1;\n}\n\n.achievement-name {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.achievement-desc {\n  font-size: 12px;\n  color: #606266;\n}\n\n.achievement-status i {\n  font-size: 18px;\n  color: #909399;\n}\n\n.achievement-item.unlocked .achievement-status i {\n  color: #67c23a;\n}\n\n.actions {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.reset-dialog-content {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.reset-dialog-content p {\n  margin: 15px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .stats {\n    padding: 0 10px;\n  }\n  \n  .progress-content {\n    flex-direction: column;\n    gap: 20px;\n  }\n  \n  .items-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .chart-bar {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n  \n  .bar-container {\n    width: 100%;\n  }\n}\n</style>\n"]}]}