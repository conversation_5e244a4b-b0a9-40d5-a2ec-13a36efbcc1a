{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue", "mtime": 1756199973404}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Delivery.vue"], "names": [], "mappings": ";AA8KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Delivery.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"delivery\">\n    <!-- 投递状态卡片 -->\n    <el-card class=\"status-card\" shadow=\"hover\">\n      <div class=\"delivery-status\">\n        <div class=\"status-info\">\n          <h2>投递状态</h2>\n          <div class=\"status-details\">\n            <div class=\"status-item\">\n              <i class=\"el-icon-calendar\"></i>\n              <span>第 {{ gameStatus.currentDay || 1 }} 天</span>\n            </div>\n            <div class=\"status-item\">\n              <i class=\"el-icon-box\"></i>\n              <span>总投递: {{ gameStatus.totalDeliveries || 0 }} 次</span>\n            </div>\n            <div class=\"status-item\">\n              <i :class=\"gameStatus.canDeliverToday ? 'el-icon-check' : 'el-icon-close'\"></i>\n              <span :class=\"{ 'can-deliver': gameStatus.canDeliverToday, 'cannot-deliver': !gameStatus.canDeliverToday }\">\n                {{ gameStatus.canDeliverToday ? '今日可投递' : '今日已投递' }}\n              </span>\n            </div>\n          </div>\n        </div>\n        <div class=\"status-icon\">\n          <i class=\"el-icon-present\" :class=\"{ 'active': gameStatus.canDeliverToday }\"></i>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 投递表单 -->\n    <el-card class=\"delivery-form-card\" shadow=\"hover\" v-if=\"gameStatus.canDeliverToday\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>投递新物品</span>\n        <el-tag type=\"primary\">每天只能投递一次</el-tag>\n      </div>\n      \n      <el-form \n        ref=\"deliveryForm\" \n        :model=\"deliveryForm\" \n        :rules=\"deliveryRules\" \n        label-width=\"100px\"\n        @submit.native.prevent=\"submitDelivery\"\n      >\n        <el-form-item label=\"物品名称\" prop=\"item\">\n          <el-input\n            v-model=\"deliveryForm.item\"\n            placeholder=\"请输入要投递的物品名称\"\n            maxlength=\"50\"\n            show-word-limit\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"物品描述\" prop=\"description\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"deliveryForm.description\"\n            placeholder=\"请详细描述这个物品的特点、用途或背景故事...\"\n            :rows=\"4\"\n            maxlength=\"200\"\n            show-word-limit\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button \n            type=\"primary\" \n            @click=\"submitDelivery\" \n            :loading=\"submitting\"\n            size=\"large\"\n            style=\"width: 100%;\"\n          >\n            <i class=\"el-icon-position\"></i>\n            {{ submitting ? '投递中...' : '投递物品' }}\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 无法投递提示 -->\n    <el-card class=\"no-delivery-card\" shadow=\"hover\" v-else>\n      <div class=\"no-delivery-content\">\n        <i class=\"el-icon-warning-outline\"></i>\n        <h3>今日已投递</h3>\n        <p>您今天已经投递过物品了，请明天再来吧！</p>\n        <p class=\"next-delivery-time\">\n          下次投递时间：明天 00:00\n        </p>\n        <el-button type=\"primary\" @click=\"$router.push('/story')\">\n          <i class=\"el-icon-reading\"></i>\n          查看最新故事\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 投递历史 -->\n    <el-card class=\"history-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>投递历史</span>\n        <el-button type=\"text\" @click=\"loadDeliveryHistory\">\n          <i class=\"el-icon-refresh\"></i>\n          刷新\n        </el-button>\n      </div>\n      \n      <div v-if=\"deliveryHistory.length > 0\">\n        <el-timeline>\n          <el-timeline-item\n            v-for=\"(item, index) in deliveryHistory.slice(0, 5)\"\n            :key=\"index\"\n            :timestamp=\"formatTime(item.deliveryDate)\"\n            type=\"primary\"\n            icon=\"el-icon-box\"\n          >\n            <div class=\"history-item\">\n              <h4>{{ item.name }}</h4>\n              <p>{{ item.description }}</p>\n              <el-tag size=\"mini\">第 {{ item.day }} 天</el-tag>\n            </div>\n          </el-timeline-item>\n        </el-timeline>\n        \n        <div class=\"history-more\" v-if=\"deliveryHistory.length > 5\">\n          <el-button type=\"text\" @click=\"showAllHistory = !showAllHistory\">\n            {{ showAllHistory ? '收起' : `查看全部 ${deliveryHistory.length} 条记录` }}\n          </el-button>\n        </div>\n      </div>\n      \n      <el-empty v-else description=\"还没有投递记录\" />\n    </el-card>\n\n    <!-- 投递建议 -->\n    <el-card class=\"suggestions-card\" shadow=\"hover\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>投递建议</span>\n        <i class=\"el-icon-info\"></i>\n      </div>\n      \n      <div class=\"suggestions\">\n        <el-alert\n          title=\"投递小贴士\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <ul>\n            <li>物品名称要简洁明了，便于AI理解</li>\n            <li>详细的描述能让故事更加精彩</li>\n            <li>可以投递任何你能想象到的物品</li>\n            <li>每个物品都会影响主角的荒岛生活</li>\n          </ul>\n        </el-alert>\n        \n        <div class=\"example-items\">\n          <h4>示例物品：</h4>\n          <el-tag \n            v-for=\"example in exampleItems\" \n            :key=\"example\"\n            @click=\"fillExample(example)\"\n            style=\"margin: 5px; cursor: pointer;\"\n            type=\"info\"\n          >\n            {{ example }}\n          </el-tag>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Delivery',\n  data() {\n    return {\n      gameStatus: {},\n      deliveryForm: {\n        item: '',\n        description: ''\n      },\n      deliveryRules: {\n        item: [\n          { required: true, message: '请输入物品名称', trigger: 'blur' },\n          { min: 1, max: 50, message: '物品名称长度在 1 到 50 个字符', trigger: 'blur' }\n        ],\n        description: [\n          { required: true, message: '请输入物品描述', trigger: 'blur' },\n          { min: 10, max: 200, message: '物品描述长度在 10 到 200 个字符', trigger: 'blur' }\n        ]\n      },\n      submitting: false,\n      deliveryHistory: [],\n      showAllHistory: false,\n      exampleItems: [\n        '瑞士军刀', '太阳能充电器', '种子包', '钓鱼竿', \n        '医疗包', '望远镜', '打火石', '净水器',\n        '帐篷', '指南针', '收音机', '绳索'\n      ]\n    }\n  },\n  async created() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadGameStatus(),\n        this.loadDeliveryHistory()\n      ])\n    },\n    \n    async loadGameStatus() {\n      try {\n        const response = await this.$http.get('/game/status')\n        if (response.data.success) {\n          this.gameStatus = response.data.data\n          this.$emit('game-status-updated', this.gameStatus)\n        }\n      } catch (error) {\n        console.error('Failed to load game status:', error)\n      }\n    },\n    \n    async loadDeliveryHistory() {\n      try {\n        const response = await this.$http.get('/delivery/history')\n        if (response.data.success) {\n          this.deliveryHistory = response.data.data.items || []\n        }\n      } catch (error) {\n        console.error('Failed to load delivery history:', error)\n      }\n    },\n    \n    async submitDelivery() {\n      try {\n        await this.$refs.deliveryForm.validate()\n        \n        this.submitting = true\n        \n        const response = await this.$http.post('/delivery', {\n          item: this.deliveryForm.item.trim(),\n          description: this.deliveryForm.description.trim()\n        })\n        \n        if (response.data.success) {\n          this.$message.success('物品投递成功！新的故事章节已生成')\n          \n          // 重置表单\n          this.deliveryForm = { item: '', description: '' }\n          this.$refs.deliveryForm.resetFields()\n          \n          // 更新状态\n          await this.loadData()\n          \n          // 跳转到故事页面查看新章节\n          this.$router.push('/story')\n        }\n      } catch (error) {\n        if (error.response && error.response.data) {\n          this.$message.error(error.response.data.error || '投递失败')\n        } else {\n          this.$message.error('投递失败，请重试')\n        }\n      } finally {\n        this.submitting = false\n      }\n    },\n    \n    fillExample(example) {\n      if (this.gameStatus.canDeliverToday) {\n        this.deliveryForm.item = example\n        this.$refs.deliveryForm.validateField('item')\n      }\n    },\n    \n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.delivery {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.status-card {\n  margin-bottom: 20px;\n}\n\n.delivery-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.status-info h2 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.status-details {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #606266;\n}\n\n.status-item i {\n  width: 16px;\n}\n\n.can-deliver {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.cannot-deliver {\n  color: #f56c6c;\n}\n\n.status-icon {\n  font-size: 60px;\n  color: #ddd;\n}\n\n.status-icon .active {\n  color: #409eff;\n}\n\n.delivery-form-card,\n.no-delivery-card,\n.history-card,\n.suggestions-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.no-delivery-content {\n  text-align: center;\n  padding: 40px 20px;\n}\n\n.no-delivery-content i {\n  font-size: 60px;\n  color: #f56c6c;\n  margin-bottom: 20px;\n}\n\n.no-delivery-content h3 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.no-delivery-content p {\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.next-delivery-time {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 20px !important;\n}\n\n.history-item h4 {\n  color: #303133;\n  margin-bottom: 5px;\n}\n\n.history-item p {\n  color: #606266;\n  margin-bottom: 8px;\n}\n\n.history-more {\n  text-align: center;\n  margin-top: 15px;\n}\n\n.suggestions ul {\n  margin: 15px 0;\n  padding-left: 20px;\n}\n\n.suggestions li {\n  margin-bottom: 5px;\n  color: #606266;\n}\n\n.example-items {\n  margin-top: 20px;\n}\n\n.example-items h4 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .delivery {\n    padding: 0 10px;\n  }\n  \n  .delivery-status {\n    flex-direction: column;\n    text-align: center;\n    gap: 20px;\n  }\n  \n  .status-details {\n    align-items: center;\n  }\n}\n</style>\n"]}]}