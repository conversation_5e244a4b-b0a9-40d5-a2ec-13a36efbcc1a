{"remainingRequest": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue?vue&type=template&id=5c98942e&scoped=true", "dependencies": [{"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\src\\views\\Delivery.vue", "mtime": 1756199973404}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1756201382617}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756201380282}, {"path": "E:\\work_code\\xiaoshuo_generator\\frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756201382067}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}