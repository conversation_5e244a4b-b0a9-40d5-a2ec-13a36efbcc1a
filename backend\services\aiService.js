const axios = require('axios');
const config = require('../config/config');

class AIService {
  constructor() {
    this.apiUrl = config.deepseek.apiUrl;
    this.apiKey = config.deepseek.apiKey;
    this.model = config.deepseek.model;
  }

  // 生成新的故事章节
  async generateStoryChapter(currentStory, newItem, itemDescription, dayNumber) {
    try {
      if (!this.apiKey) {
        throw new Error('DeepSeek API key is not configured');
      }

      // 构建当前故事内容
      const storyContent = this.buildStoryContent(currentStory);
      
      // 构建提示词
      const prompt = config.game.storyPromptTemplate
        .replace('{currentStory}', storyContent)
        .replace('{newItem}', newItem)
        .replace('{itemDescription}', itemDescription)
        .replace('{dayNumber}', dayNumber);

      const requestData = {
        model: this.model,
        messages: [
          {
            role: "system",
            content: "你是一个专业的小说作家，擅长写生存冒险类小说。请根据用户提供的背景和物品，创作引人入胜的故事章节。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        stream: false,
        temperature: 0.8,
        max_tokens: 1000
      };

      const response = await axios.post(this.apiUrl, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 30000 // 30秒超时
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        const generatedContent = response.data.choices[0].message.content.trim();
        return {
          success: true,
          content: generatedContent,
          usage: response.data.usage
        };
      } else {
        throw new Error('Invalid response format from AI service');
      }

    } catch (error) {
      console.error('Error generating story chapter:', error);
      
      // 如果AI服务失败，返回一个备用的故事内容
      const fallbackContent = this.generateFallbackChapter(newItem, itemDescription, dayNumber);
      
      return {
        success: false,
        content: fallbackContent,
        error: error.message,
        fallback: true
      };
    }
  }

  // 构建当前故事内容摘要
  buildStoryContent(storyData) {
    if (!storyData || !storyData.chapters) {
      return "故事刚刚开始...";
    }

    // 获取最近的几个章节作为上下文
    const recentChapters = storyData.chapters.slice(-3);
    
    return recentChapters.map(chapter => {
      const itemInfo = chapter.deliveredItem ? 
        `（收到物品：${chapter.deliveredItem.name}）` : '';
      return `第${chapter.day}天${itemInfo}：${chapter.content.substring(0, 200)}...`;
    }).join('\n\n');
  }

  // 生成备用故事内容（当AI服务不可用时）
  generateFallbackChapter(newItem, itemDescription, dayNumber) {
    const fallbackTemplates = [
      // 模板1：详细的发现和使用过程
      `第${dayNumber}天的清晨，海风轻抚着李明的脸庞，将他从睡梦中唤醒。他已经习惯了每天早晨到海滩上寻找那个神秘的包裹，这已经成为他在荒岛上生活的重要仪式。

今天，当他走到熟悉的投递点时，果然又发现了一个新的包裹。李明小心翼翼地打开包装，里面是${newItem}。${itemDescription}

看到这个物品，李明的眼中闪过一丝惊喜。在这个与世隔绝的荒岛上，每一样物品都可能成为生存的关键。他仔细研究着这个${newItem}，思考着如何最好地利用它。

上午时分，李明开始尝试使用这个新物品。经过一番摸索，他发现它确实能够帮助改善自己的生存状况。这让他对那个神秘的送礼人更加感激，同时也更加好奇对方的身份。

下午，李明利用${newItem}完成了一些之前难以完成的任务。夕阳西下时，他坐在海滩上，望着远方的海平线，心中既有对未来的期待，也有对明天会收到什么礼物的好奇。

夜幕降临，李明在篝火旁整理着自己的物品，${newItem}被他小心地收藏起来。他知道，明天又会是新的一天，又会有新的惊喜等着他。`,

      // 模板2：重点描述物品的实际应用
      `第${dayNumber}天，李明像往常一样在黎明时分醒来。海岛上的生活让他的作息变得非常规律，每天都充满了对新包裹的期待。

当他来到海滩的投递点时，新的包裹已经在那里等着他了。打开包裹，李明发现了${newItem}。${itemDescription}这个发现让他兴奋不已。

李明立即意识到这个物品的价值。在荒岛上的这些日子里，他已经学会了如何充分利用每一样物品。他开始仔细规划如何使用这个${newItem}来改善自己的生活条件。

整个上午，李明都在忙碌着。他利用${newItem}解决了一些长期困扰他的问题，这让他的生存环境得到了明显的改善。看着自己的劳动成果，李明感到了久违的成就感。

午后的阳光格外温暖，李明在树荫下休息，手中把玩着这个珍贵的${newItem}。他想象着那个神秘的朋友，想知道对方是如何知道他需要什么的。

傍晚时分，李明利用${newItem}完成了今天的最后一项任务。当夜幕降临时，他满足地看着自己改善后的生活环境，心中充满了对明天的期待。`,

      // 模板3：强调心理变化和环境描写
      `第${dayNumber}天的荒岛依然美丽而神秘。李明在鸟儿的啁啾声中醒来，心情比前几天更加轻松。经过这些天的适应，他已经逐渐习惯了这种与世隔绝的生活。

按照惯例，李明来到海滩寻找今天的包裹。当他看到包裹中的${newItem}时，不禁露出了微笑。${itemDescription}这个物品的出现，仿佛是对他近期努力的奖励。

李明花了一些时间研究这个${newItem}，思考着它能为自己的荒岛生活带来什么改变。他的生存技能在这些天里得到了很大提升，现在他能够更加高效地利用每一样物品。

在使用${newItem}的过程中，李明发现它不仅实用，还让他想起了文明世界的生活。这种怀念并没有让他沮丧，反而让他更加珍惜现在拥有的一切。

随着一天的推移，${newItem}确实为李明的生活带来了便利。当夜晚来临时，他坐在篝火旁，凝视着跳跃的火焰，心中对那个神秘的朋友充满了感激。

在满天繁星的陪伴下，李明进入了梦乡。他梦见了明天的包裹，梦见了新的冒险，梦见了这个荒岛上还有更多的秘密等待他去发现。`
    ];

    const randomIndex = Math.floor(Math.random() * fallbackTemplates.length);
    return fallbackTemplates[randomIndex];
  }

  // 检查API连接状态
  async checkConnection() {
    try {
      if (!this.apiKey) {
        return { connected: false, error: 'API key not configured' };
      }

      const testData = {
        model: this.model,
        messages: [
          { role: "user", content: "Hello" }
        ],
        max_tokens: 10
      };

      const response = await axios.post(this.apiUrl, testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 10000
      });

      return { connected: true, model: this.model };
    } catch (error) {
      return { 
        connected: false, 
        error: error.message,
        fallbackAvailable: true
      };
    }
  }
}

module.exports = new AIService();
