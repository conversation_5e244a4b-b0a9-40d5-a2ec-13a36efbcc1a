const axios = require('axios');
const config = require('../config/config');

class AIService {
  constructor() {
    this.apiUrl = config.deepseek.apiUrl;
    this.apiKey = config.deepseek.apiKey;
    this.model = config.deepseek.model;
  }

  // 生成新的故事章节
  async generateStoryChapter(currentStory, newItem, itemDescription, dayNumber) {
    try {
      if (!this.apiKey) {
        throw new Error('DeepSeek API key is not configured');
      }

      // 构建当前故事内容
      const storyContent = this.buildStoryContent(currentStory);
      
      // 构建提示词
      const prompt = config.game.storyPromptTemplate
        .replace('{currentStory}', storyContent)
        .replace('{newItem}', newItem)
        .replace('{itemDescription}', itemDescription)
        .replace('{dayNumber}', dayNumber);

      const requestData = {
        model: this.model,
        messages: [
          {
            role: "system",
            content: "你是一个专业的小说作家，擅长写生存冒险类小说。请根据用户提供的背景和物品，创作引人入胜的故事章节。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        stream: false,
        temperature: 0.8,
        max_tokens: 1000
      };

      const response = await axios.post(this.apiUrl, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 30000 // 30秒超时
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        const generatedContent = response.data.choices[0].message.content.trim();
        return {
          success: true,
          content: generatedContent,
          usage: response.data.usage
        };
      } else {
        throw new Error('Invalid response format from AI service');
      }

    } catch (error) {
      console.error('Error generating story chapter:', error);
      
      // 如果AI服务失败，返回一个备用的故事内容
      const fallbackContent = this.generateFallbackChapter(newItem, itemDescription, dayNumber);
      
      return {
        success: false,
        content: fallbackContent,
        error: error.message,
        fallback: true
      };
    }
  }

  // 构建当前故事内容摘要
  buildStoryContent(storyData) {
    if (!storyData || !storyData.chapters) {
      return "故事刚刚开始...";
    }

    // 获取最近的几个章节作为上下文
    const recentChapters = storyData.chapters.slice(-3);
    
    return recentChapters.map(chapter => {
      const itemInfo = chapter.deliveredItem ? 
        `（收到物品：${chapter.deliveredItem.name}）` : '';
      return `第${chapter.day}天${itemInfo}：${chapter.content.substring(0, 200)}...`;
    }).join('\n\n');
  }

  // 生成备用故事内容（当AI服务不可用时）
  generateFallbackChapter(newItem, itemDescription, dayNumber) {
    const fallbackTemplates = [
      `第${dayNumber}天，李明在海滩上发现了一个新的包裹。打开后，里面是${newItem}。${itemDescription}这个物品让他想起了过去的生活，也为他在岛上的生存带来了新的可能性。他小心地收好这个珍贵的物品，心中对神秘的送礼人充满了感激。`,
      
      `清晨的阳光洒在海滩上，李明照例来到包裹投递点。今天的包裹里装着${newItem}，${itemDescription}这让他既惊喜又困惑。这个物品似乎正好解决了他最近遇到的问题。他开始思考，这个神秘的朋友是如何知道他的需求的？`,
      
      `第${dayNumber}天的包裹给李明带来了${newItem}。${itemDescription}虽然这个物品看起来普通，但在这个荒岛上，任何东西都可能成为生存的关键。李明决定好好利用这个礼物，继续他的荒岛生活。`
    ];

    const randomIndex = Math.floor(Math.random() * fallbackTemplates.length);
    return fallbackTemplates[randomIndex];
  }

  // 检查API连接状态
  async checkConnection() {
    try {
      if (!this.apiKey) {
        return { connected: false, error: 'API key not configured' };
      }

      const testData = {
        model: this.model,
        messages: [
          { role: "user", content: "Hello" }
        ],
        max_tokens: 10
      };

      const response = await axios.post(this.apiUrl, testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 10000
      });

      return { connected: true, model: this.model };
    } catch (error) {
      return { 
        connected: false, 
        error: error.message,
        fallbackAvailable: true
      };
    }
  }
}

module.exports = new AIService();
