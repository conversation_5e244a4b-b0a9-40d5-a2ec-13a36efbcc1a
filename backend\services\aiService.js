const axios = require('axios');
const config = require('../config/config');

class AIService {
  constructor() {
    this.apiUrl = config.deepseek.apiUrl;
    this.apiKey = config.deepseek.apiKey;
    this.model = config.deepseek.model;
  }

  // 生成新的故事章节
  async generateStoryChapter(currentStory, newItem, itemDescription, dayNumber) {
    try {
      if (!this.apiKey) {
        throw new Error('DeepSeek API key is not configured');
      }

      // 构建当前故事内容
      const storyContent = this.buildStoryContent(currentStory);
      
      // 构建提示词
      const prompt = config.game.storyPromptTemplate
        .replace('{currentStory}', storyContent)
        .replace('{newItem}', newItem)
        .replace('{itemDescription}', itemDescription)
        .replace('{dayNumber}', dayNumber);

      const requestData = {
        model: this.model,
        messages: [
          {
            role: "system",
            content: "你是一个专业的小说作家，擅长写生存冒险类小说。请根据用户提供的背景和物品，创作引人入胜的故事章节。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        stream: false,
        temperature: 0.8,
        max_tokens: 1000
      };

      const response = await axios.post(this.apiUrl, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 30000 // 30秒超时
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        const generatedContent = response.data.choices[0].message.content.trim();
        return {
          success: true,
          content: generatedContent,
          usage: response.data.usage
        };
      } else {
        throw new Error('Invalid response format from AI service');
      }

    } catch (error) {
      console.error('Error generating story chapter:', error);
      
      // 如果AI服务失败，返回一个备用的故事内容
      const fallbackContent = this.generateFallbackChapter(newItem, itemDescription, dayNumber);
      
      return {
        success: false,
        content: fallbackContent,
        error: error.message,
        fallback: true
      };
    }
  }

  // 构建当前故事内容摘要
  buildStoryContent(storyData) {
    if (!storyData || !storyData.chapters) {
      return "故事刚刚开始...";
    }

    // 获取最近的几个章节作为上下文
    const recentChapters = storyData.chapters.slice(-3);
    
    return recentChapters.map(chapter => {
      const itemInfo = chapter.deliveredItem ? 
        `（收到物品：${chapter.deliveredItem.name}）` : '';
      return `第${chapter.day}天${itemInfo}：${chapter.content.substring(0, 200)}...`;
    }).join('\n\n');
  }

  // 生成备用故事内容（当AI服务不可用时）
  generateFallbackChapter(newItem, itemDescription, dayNumber) {
    const fallbackTemplates = [
      // 模板1：海浪冲上岸的发现
      `第${dayNumber}天的清晨，李明被海浪拍打礁石的声音唤醒。昨夜的暴风雨已经过去，但海面依然波涛汹涌。当他走向海滩寻找可能被冲上岸的有用物品时，在一堆海藻中发现了${newItem}。${itemDescription}

李明兴奋地捡起这个物品，仔细检查着它的状况。在荒岛上的这些日子里，他已经学会了珍惜每一样可能有用的东西。他立即意识到这个${newItem}可以解决他目前面临的一些生存难题。

上午，李明开始实验如何使用这个新发现的物品。经过反复尝试，他找到了几种巧妙的使用方法。这个${newItem}不仅能够帮助他更有效地获取食物，还能改善他的居住条件。

下午时分，李明利用${newItem}成功完成了一项重要的生存任务。看着自己的劳动成果，他感到了久违的成就感。这个小小的发现让他对在这个荒岛上继续生存下去更有信心了。

傍晚，李明坐在自己搭建的庇护所前，手中把玩着这个珍贵的${newItem}。夕阳西下，他望着远方的海平线，心中既有对家乡的思念，也有对明天新挑战的期待。

夜幕降临，李明在篝火旁整理着自己的生存装备。${newItem}被他小心地收好，成为了他荒岛求生工具箱中的重要一员。`,

      // 模板2：在岛上探索时发现
      `第${dayNumber}天，李明决定探索岛屿的另一个区域。经过几天的观察，他注意到岛屿西侧有一片他还没有仔细搜索过的区域。带着自制的工具，他开始了新的探索之旅。

在一处被藤蔓覆盖的岩石缝隙中，李明意外地发现了${newItem}。${itemDescription}这个发现让他惊喜万分，仿佛是荒岛给他的一份珍贵礼物。

李明仔细研究着这个物品，思考着它在荒岛生存中的各种可能用途。凭借这些天积累的生存经验，他很快就想出了几种创新的使用方法。

上午的时光，李明专注于实验这个新工具。他发现${newItem}不仅能够帮助他更高效地处理日常生存任务，还能解决一些他之前束手无策的问题。这种突破让他对自己的生存能力更加自信。

下午，李明利用${newItem}改进了自己的生存设施。他的庇护所变得更加坚固，食物储存也更加安全。看着这些改善，李明感到了真正的成就感。

夜晚，李明坐在篝火旁，凝视着手中的${newItem}。这个小小的发现不仅改善了他的生存条件，更重要的是给了他继续坚持下去的希望和动力。`,

      // 模板3：从废墟或残骸中发现
      `第${dayNumber}天，李明决定再次探索岛屿北端的那片废墟。那里似乎曾经是某种建筑的遗迹，可能是很久以前的船只残骸或者废弃的营地。

在仔细搜索一堆锈蚀的金属和腐朽木材时，李明在一个半埋在沙土中的容器里发现了${newItem}。${itemDescription}虽然经历了风吹雨打，但这个物品依然保持着良好的状态。

李明兴奋地清理着这个珍贵的发现。在荒岛上，每一个工具都可能成为生存的关键。他开始思考如何将这个${newItem}融入到自己的日常生存活动中。

经过一番实验，李明发现这个物品的用途比他想象的还要广泛。他用它改进了自己的捕鱼技巧，提高了生火的效率，甚至还用它来改善自己的住所。

下午，李明利用${newItem}成功解决了一个困扰他多日的问题。这种成功的喜悦让他更加相信，只要保持智慧和创造力，他就能在这个荒岛上生存下去。

夜晚，李明躺在自己的庇护所里，听着外面海浪的声音。手中握着${newItem}，他感到了前所未有的安全感。这个小小的发现不仅是一个工具，更是他与这个荒岛建立联系的桥梁。`
    ];

    const randomIndex = Math.floor(Math.random() * fallbackTemplates.length);
    return fallbackTemplates[randomIndex];
  }

  // 检查API连接状态
  async checkConnection() {
    try {
      if (!this.apiKey) {
        return { connected: false, error: 'API key not configured' };
      }

      const testData = {
        model: this.model,
        messages: [
          { role: "user", content: "Hello" }
        ],
        max_tokens: 10
      };

      const response = await axios.post(this.apiUrl, testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 10000
      });

      return { connected: true, model: this.model };
    } catch (error) {
      return { 
        connected: false, 
        error: error.message,
        fallbackAvailable: true
      };
    }
  }
}

module.exports = new AIService();
